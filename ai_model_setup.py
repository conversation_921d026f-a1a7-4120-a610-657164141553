#!/usr/bin/env python3
"""
AI Model Setup and Configuration
================================

This module handles AI model configuration and validation for the clinical trials analysis system.

Key Functions:
- get_available_models(): Get list of available Ollama models
- validate_model(): Check if a specific model is available
- test_model_connection(): Test model connectivity and response
- query_ollama(): Core function to query Ollama with prompts

Author: Clinical Trials Analysis System
Version: 2.0 - Clean Architecture
"""

# Standard library imports
import json
import time

# Third-party imports
import requests

# Constants
DEFAULT_OLLAMA_URL = "http://localhost:11434"
DEFAULT_TIMEOUT = 120
REASONING_MODEL_TIMEOUT = 300
MAX_RETRIES = 2

def get_available_models(ollama_url=DEFAULT_OLLAMA_URL):
    """
    Get list of available Ollama models with size information
    
    Returns:
        dict: Success status and list of models with display names
    """
    try:
        print("Fetching models from Ollama...")
        response = requests.get(f'{ollama_url}/api/tags', timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            models = data.get('models', [])
            print(f"Found {len(models)} models")
            
            model_list = []
            for model in models:
                # Handle different possible size field names and formats
                size_bytes = model.get('size', 0)
                if size_bytes and isinstance(size_bytes, (int, float)) and size_bytes > 0:
                    size_gb = size_bytes / (1024 * 1024 * 1024)
                    display_name = f"{model.get('name', '')} ({size_gb:.1f} GB)"
                else:
                    # Fallback if size is not available or invalid
                    display_name = model.get('name', '')

                model_list.append({
                    'name': model.get('name', ''),
                    'display': display_name
                })
            
            return {'success': True, 'models': model_list}
        else:
            return {'success': False, 'error': 'Ollama not responding'}
    except Exception as e:
        print(f"Error: {e}")
        return {'success': False, 'error': str(e)}

def validate_model(model_name, ollama_url=DEFAULT_OLLAMA_URL):
    """
    Validate that a specific model is available
    
    Args:
        model_name (str): Name of the model to validate
        ollama_url (str): Ollama server URL
    
    Returns:
        dict: Validation result with success status
    """
    try:
        models_result = get_available_models(ollama_url)
        if not models_result['success']:
            return {'success': False, 'error': 'Cannot connect to Ollama'}
        
        available_models = [m['name'] for m in models_result['models']]
        if model_name in available_models:
            return {'success': True, 'message': f'Model {model_name} is available'}
        else:
            return {
                'success': False, 
                'error': f'Model {model_name} not found. Available models: {", ".join(available_models)}'
            }
    except Exception as e:
        return {'success': False, 'error': str(e)}

def test_model_connection(model_name, ollama_url=DEFAULT_OLLAMA_URL):
    """
    Test model connectivity with a simple query
    
    Args:
        model_name (str): Name of the model to test
        ollama_url (str): Ollama server URL
    
    Returns:
        dict: Test result with success status and response time
    """
    try:
        start_time = time.time()
        
        test_prompt = "Respond with exactly: 'Connection test successful'"
        result = query_ollama(test_prompt, model_name, ollama_url)
        
        end_time = time.time()
        response_time = end_time - start_time
        
        if result['success']:
            return {
                'success': True,
                'message': f'Model {model_name} responded successfully',
                'response_time': f'{response_time:.2f}s',
                'response': result['response']
            }
        else:
            return {
                'success': False,
                'error': f'Model test failed: {result["error"]}'
            }
    except Exception as e:
        return {'success': False, 'error': str(e)}

def query_ollama(prompt, model_name, ollama_url=DEFAULT_OLLAMA_URL, temperature=0.1, max_tokens=2048):
    """
    Query Ollama with the analysis prompt
    
    Args:
        prompt (str): The prompt to send to the model
        model_name (str): Name of the model to use
        ollama_url (str): Ollama server URL
        temperature (float): Model temperature setting
        max_tokens (int): Maximum tokens for response
    
    Returns:
        dict: Query result with success status and response
    """
    # Set longer timeout for reasoning models like deepseek-r1
    timeout_seconds = REASONING_MODEL_TIMEOUT if 'deepseek-r1' in model_name else DEFAULT_TIMEOUT
    max_retries = MAX_RETRIES
    
    for attempt in range(max_retries):
        try:
            print(f"🤖 Querying {model_name} (attempt {attempt + 1}/{max_retries})...")
            
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": temperature,
                    "num_predict": max_tokens
                }
            }
            
            response = requests.post(
                f"{ollama_url}/api/generate",
                json=payload,
                timeout=timeout_seconds
            )
            
            if response.status_code == 200:
                result = response.json()
                response_text = result.get('response', '').strip()
                
                if response_text:
                    return {
                        'success': True,
                        'response': response_text,
                        'model': model_name,
                        'attempt': attempt + 1
                    }
                else:
                    print(f"⚠️ Empty response from {model_name}")
                    if attempt < max_retries - 1:
                        continue
                    else:
                        return {
                            'success': False,
                            'error': f'Empty response from {model_name} after {max_retries} attempts'
                        }
            else:
                error_msg = f"HTTP {response.status_code}: {response.text}"
                print(f"❌ Error from {model_name}: {error_msg}")
                if attempt < max_retries - 1:
                    continue
                else:
                    return {
                        'success': False,
                        'error': error_msg
                    }
                    
        except requests.exceptions.Timeout:
            print(f"⏰ Timeout querying {model_name} (attempt {attempt + 1})")
            if attempt < max_retries - 1:
                continue
            else:
                return {
                    'success': False,
                    'error': f'Timeout after {timeout_seconds}s'
                }
        except Exception as e:
            print(f"❌ Exception querying {model_name}: {str(e)}")
            if attempt < max_retries - 1:
                continue
            else:
                return {
                    'success': False,
                    'error': str(e)
                }
    
    return {
        'success': False,
        'error': f'Failed after {max_retries} attempts'
    }

def get_model_info(model_name, ollama_url=DEFAULT_OLLAMA_URL):
    """
    Get detailed information about a specific model
    
    Args:
        model_name (str): Name of the model
        ollama_url (str): Ollama server URL
    
    Returns:
        dict: Model information including size, parameters, etc.
    """
    try:
        response = requests.post(
            f"{ollama_url}/api/show",
            json={"name": model_name},
            timeout=10
        )
        
        if response.status_code == 200:
            return {
                'success': True,
                'info': response.json()
            }
        else:
            return {
                'success': False,
                'error': f'Could not get info for model {model_name}'
            }
    except Exception as e:
        return {
            'success': False,
            'error': str(e)
        }

if __name__ == "__main__":
    # Test the AI model setup
    print("🧪 Testing AI Model Setup...")
    
    # Test connection to Ollama
    models = get_available_models()
    if models['success']:
        print(f"✅ Found {len(models['models'])} models")
        
        # Test first available model
        if models['models']:
            test_model = models['models'][0]['name']
            print(f"🧪 Testing model: {test_model}")
            
            test_result = test_model_connection(test_model)
            if test_result['success']:
                print(f"✅ Model test successful: {test_result['response_time']}")
            else:
                print(f"❌ Model test failed: {test_result['error']}")
    else:
        print(f"❌ Cannot connect to Ollama: {models['error']}")
