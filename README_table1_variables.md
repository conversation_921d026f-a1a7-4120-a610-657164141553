# Clinical Trials Variables Documentation

## Overview

This document provides comprehensive documentation for all variables extracted and generated from clinical trials data, including raw variables, meta-variables, and characteristics for Table 1.

## File Structure

### Generated Files
- **`raw_variables.csv`** - 122 raw variables extracted directly from JSON modules
- **`meta_variables.csv`** - 52 derived meta-variables for meta-analysis
- **`characteristics_of_included_studies.csv`** - 20 critical characteristics for Table 1
- **`all_categorical_variables.csv`** - 25 categorical meta-variables for subgroup analysis
- **`all_continuous_variables.csv`** - 29 continuous meta-variables for meta-regression

### R_output Folder (with table1_ prefix)
- **`table1_raw_variables.csv`**
- **`table1_meta_variables.csv`**
- **`table1_characteristics_of_included_studies.csv`**
- **`all_categorical_variables.csv`**
- **`all_continuous_variables.csv`**

---

## RAW VARIABLES (122 Variables)

### Module 1: Identification Module (8 variables)
- `nctId` - NCT identifier
- `organization_fullName` - Organization name
- `briefTitle` - Study title
- `officialTitle` - Official title
- `acronym` - Study acronym
- `orgStudyIdInfo_id` - Organization study ID
- `secondaryIdInfos_count` - Number of secondary IDs
- `secondaryIdInfos_ids` - Secondary ID list

### Module 2: Status Module (8 variables)
- `overallStatus` - Overall study status
- `whyStopped` - Reason if stopped
- `startDateStruct_date` - Start date
- `startDateStruct_type` - Start date type
- `primaryCompletionDateStruct_date` - Primary completion date
- `primaryCompletionDateStruct_type` - Primary completion date type
- `completionDateStruct_date` - Study completion date
- `completionDateStruct_type` - Study completion date type

### Module 3: Design Module (9 variables)
- `studyType` - Study type (Interventional/Observational)
- `phases` - Study phases
- `designInfo_allocation` - Allocation method
- `designInfo_interventionModel` - Intervention model
- `designInfo_primaryPurpose` - Primary purpose
- `designInfo_maskingInfo_masking` - Masking type
- `designInfo_maskingInfo_maskingDescription` - Masking description
- `enrollmentInfo_count` - Planned enrollment
- `enrollmentInfo_type` - Enrollment type

### Module 4: Arms and Interventions Module (6 variables)
- `armGroups_count` - Number of study arms
- `armGroups_labels` - Arm labels
- `armGroups_types` - Arm types
- `interventions_count` - Number of interventions
- `interventions_types` - Intervention types
- `interventions_names` - Intervention names

### Module 5: Conditions Module (3 variables)
- `conditions` - Study conditions
- `keywords` - Study keywords
- `conditions_count` - Number of conditions

### Module 6: Eligibility Module (6 variables)
- `eligibilityCriteria` - Eligibility criteria text
- `healthyVolunteers` - Healthy volunteers accepted
- `sex` - Gender eligibility
- `minimumAge` - Minimum age
- `maximumAge` - Maximum age
- `stdAges` - Standard age groups

### Module 7: Contacts and Locations Module (6 variables)
- `locations_count` - Number of locations
- `locations_countries` - Countries list
- `locations_facilities` - Facility names
- `centralContacts_count` - Number of central contacts
- `overallOfficials_count` - Number of overall officials
- `overallOfficials_names` - Official names

### Module 8: Outcomes Module (6 variables)
- `primaryOutcomes_count` - Number of primary outcomes
- `primaryOutcomes_measures` - Primary outcome measures
- `primaryOutcomes_descriptions` - Primary outcome descriptions
- `secondaryOutcomes_count` - Number of secondary outcomes
- `secondaryOutcomes_measures` - Secondary outcome measures
- `secondaryOutcomes_descriptions` - Secondary outcome descriptions

### Module 9: Sponsor Collaborators Module (4 variables)
- `leadSponsor_name` - Lead sponsor name
- `leadSponsor_class` - Lead sponsor class
- `collaborators_count` - Number of collaborators
- `collaborators_names` - Collaborator names

### Module 10: Oversight Module (4 variables)
- `oversightHasDmc` - Has data monitoring committee
- `isFdaRegulatedDrug` - FDA regulated drug
- `isFdaRegulatedDevice` - FDA regulated device
- `isUnapprovedDevice` - Unapproved device

### Module 11: Description Module (2 variables)
- `briefSummary` - Brief summary
- `detailedDescription` - Detailed description

### Module 12: References Module (2 variables)
- `references_count` - Number of references
- `seeAlsoLinks_count` - Number of see also links

### Module 13: Baseline Characteristics Module (6 variables)
- `baseline_total_participants` - Total participants
- `baseline_age_mean` - Mean age
- `baseline_age_sd` - Age standard deviation
- `baseline_sex_male_count` - Male participants count
- `baseline_sex_female_count` - Female participants count
- `baseline_sex_total_count` - Total sex count

### Module 14: Participant Flow Module (8 variables)
- `participantFlow_started_total` - Participants started
- `participantFlow_completed_total` - Participants completed
- `participantFlow_notCompleted_total` - Participants not completed
- `participantFlow_dropouts_adverseEvent` - AE dropouts
- `participantFlow_dropouts_lackEfficacy` - Efficacy dropouts
- `participantFlow_dropouts_lostFollowup` - Lost to follow-up
- `participantFlow_dropouts_withdrawal` - Withdrawals
- `participantFlow_dropouts_other` - Other dropouts

### Module 15: Outcome Measures Module (3 variables)
- `outcomeMeasures_count` - Number of outcome measures
- `outcomeMeasures_titles` - Outcome measure titles
- `outcomeMeasures_descriptions` - Outcome measure descriptions

### Module 16: Adverse Events Module (6 variables)
- `adverseEvents_groups_count` - AE groups count
- `adverseEvents_seriousEvents_count` - Serious events count
- `adverseEvents_otherEvents_count` - Other events count
- `adverseEvents_seriousEvents_categories` - Serious event categories
- `adverseEvents_otherEvents_categories` - Other event categories
- `adverseEvents_timeFrame` - AE time frame

### Module 17: More Info Module (4 variables)
- `moreInfoModule_limitationsAndCaveats` - Limitations
- `moreInfoModule_certainAgreements` - Agreements
- `moreInfoModule_pointOfContact` - Point of contact
- `moreInfoModule_responsibleParty` - Responsible party

### Module 18: Derived Section (15 variables)
- `resultsFirstPostDate` - Results first posted date
- `resultsFirstPostDateStruct_date` - Results posted date
- `resultsFirstSubmitDate` - Results first submitted date
- `lastUpdatePostDate` - Last update posted date
- `lastUpdatePostDateStruct_date` - Last update date
- `lastUpdateSubmitDate` - Last update submitted date
- `studyFirstPostDate` - Study first posted date
- `studyFirstPostDateStruct_date` - Study first posted date
- `studyFirstSubmitDate` - Study first submitted date
- `studyFirstSubmitQcDate` - Study first QC date
- `lastKnownStatus` - Last known status
- `delayedPosting` - Delayed posting
- `expandedAccessInfo` - Expanded access info
- `hasExpandedAccess` - Has expanded access
- `ipdSharing` - IPD sharing

---

## META-VARIABLES (52 Variables)

### A. TEMPORAL META-VARIABLES (4 variables)
1. **`study_duration_months`** - Total study duration from start to completion
2. **`primary_completion_duration_months`** - Duration from start to primary completion
3. **`time_to_results_posting_months`** - Time from completion to results posting
4. **`regulatory_review_time_months`** - Regulatory review time for results

### B. DEMOGRAPHIC META-VARIABLES (7 variables)
5. **`baseline_age_mean_numeric`** - Mean age as numeric value
6. **`baseline_age_sd_numeric`** - Age standard deviation as numeric
7. **`male_percentage`** - Percentage of male participants
8. **`female_percentage`** - Percentage of female participants
9. **`age_category`** - Young (<50), Middle (50-65), Older (>65)
10. **`sample_size_category`** - Small (<100), Medium (100-500), Large (>500)
11. **`gender_balance_category`** - Male-dominated (>60%), Balanced (40-60%), Female-dominated (<40%)

### C. STUDY DESIGN META-VARIABLES (8 variables)
12. **`design_complexity_score`** - Composite score: arms + interventions + outcomes
13. **`intervention_complexity_score`** - Number of interventions
14. **`outcome_complexity_score`** - Primary outcomes + (secondary × 0.5)
15. **`masking_quality_category`** - None, Single, Double, Triple, Quadruple
16. **`allocation_method`** - Randomized, Non-randomized
17. **`study_phase_category`** - Phase 1, Phase 2, Phase 3, Phase 4, Phase 1/2, Phase 2/3, Phase 3/4
18. **`intervention_model_category`** - PARALLEL, CROSSOVER, SEQUENTIAL, etc.
19. **`primary_purpose_category`** - TREATMENT, PREVENTION, DIAGNOSTIC, etc.

### D. PARTICIPANT FLOW META-VARIABLES (7 variables)
20. **`completion_rate`** - Percentage who completed study
21. **`dropout_rate`** - Percentage who dropped out
22. **`adverse_event_dropout_rate`** - Percentage dropouts due to AEs
23. **`efficacy_dropout_rate`** - Percentage dropouts due to lack of efficacy
24. **`lost_followup_rate`** - Percentage lost to follow-up
25. **`completion_rate_category`** - High (>90%), Medium (80-90%), Low (<80%)
26. **`dropout_pattern_category`** - AE-driven, Efficacy-driven, Mixed, Other

### E. GEOGRAPHIC AND REGULATORY META-VARIABLES (7 variables)
27. **`number_of_countries`** - Number of countries
28. **`number_of_locations`** - Total number of locations
29. **`locations_per_country`** - Average locations per country
30. **`geographic_scope_category`** - Single-country, Regional (2-5), Global (>5)
31. **`regulatory_region_category`** - US-only, EU-only, Asia-Pacific, Global
32. **`fda_regulation_category`** - None, Drug, Device, Both
33. **`regulatory_oversight_category`** - With DMC, Without DMC

### F. SPONSORSHIP AND FUNDING META-VARIABLES (3 variables)
34. **`sponsor_type_category`** - Industry, Academic, Government, Other
35. **`collaboration_level_category`** - Solo (0), Limited (1-2), Extensive (>2)
36. **`total_sponsor_entities`** - Total number of sponsoring entities

### G. OUTCOME AND EFFICACY META-VARIABLES (3 variables)
37. **`total_outcome_measures`** - Total primary + secondary outcomes
38. **`primary_to_secondary_ratio`** - Ratio of primary to secondary outcomes
39. **`outcome_focus_category`** - Primary-focused, Balanced, Secondary-heavy

### H. SAFETY AND ADVERSE EVENTS META-VARIABLES (4 variables)
40. **`adverse_events_reporting_completeness`** - Completeness score (0-100%)
41. **`serious_ae_rate`** - Rate of serious adverse events (placeholder)
42. **`total_ae_rate`** - Total adverse event rate (placeholder)
43. **`safety_reporting_category`** - Complete (>80%), Partial (40-80%), Minimal (<40%)

### I. DATA COMPLETENESS AND QUALITY META-VARIABLES (5 variables)
44. **`protocol_completeness_score`** - Protocol data completeness (0-100%)
45. **`results_completeness_score`** - Results data completeness (0-100%)
46. **`overall_data_completeness_score`** - Overall data completeness (0-100%)
47. **`data_quality_category`** - >90%, 70-90%, <70%
48. **`results_availability_category`** - >80%, 40-80%, <40%

### J. STUDY CHARACTERISTICS FOR SUBGROUP ANALYSIS (4 variables)
49. **`enrollment_size_tertiles`** - Small (<200), Medium (200-600), Large (>600)
50. **`study_duration_tertiles`** - Short (<12 months), Medium (12-24), Long (>24)
51. **`study_era_category`** - Early (pre-2010), Middle (2010-2015), Recent (>2015)
52. **`masking_effectiveness_category`** - Effective (Double+), Limited (Single), None

---

## META-VARIABLES TO RAW VARIABLES MAPPING

### A. TEMPORAL META-VARIABLES
1. **`study_duration_months`**
   - **Raw Variables**: `completionDateStruct_date`, `startDateStruct_date`
   - **Calculation**: Date difference in months

2. **`primary_completion_duration_months`**
   - **Raw Variables**: `primaryCompletionDateStruct_date`, `startDateStruct_date`
   - **Calculation**: Date difference in months

3. **`time_to_results_posting_months`**
   - **Raw Variables**: `resultsFirstPostDateStruct_date`, `completionDateStruct_date`
   - **Calculation**: Date difference in months

4. **`regulatory_review_time_months`**
   - **Raw Variables**: `resultsFirstPostDateStruct_date`, `resultsFirstSubmitDate`
   - **Calculation**: Date difference in months

### B. DEMOGRAPHIC META-VARIABLES
5. **`baseline_age_mean_numeric`**
   - **Raw Variables**: `baseline_age_mean`
   - **Calculation**: Convert to numeric, handle text formats

6. **`baseline_age_sd_numeric`**
   - **Raw Variables**: `baseline_age_sd`
   - **Calculation**: Convert to numeric, handle text formats

7. **`male_percentage`**
   - **Raw Variables**: `baseline_sex_male_count`, `baseline_total_participants`
   - **Calculation**: (male_count / total_participants) × 100

8. **`female_percentage`**
   - **Raw Variables**: `baseline_sex_female_count`, `baseline_total_participants`
   - **Calculation**: (female_count / total_participants) × 100

9. **`age_category`**
   - **Raw Variables**: `baseline_age_mean_numeric` (derived)
   - **Calculation**: <50 = Young, 50-65 = Middle, >65 = Older

10. **`sample_size_category`**
    - **Raw Variables**: `baseline_total_participants`
    - **Calculation**: <100 = Small, 100-500 = Medium, >500 = Large

11. **`gender_balance_category`**
    - **Raw Variables**: `male_percentage` (derived)
    - **Calculation**: >60% = Male-dominated, 40-60% = Balanced, <40% = Female-dominated

### C. STUDY DESIGN META-VARIABLES
12. **`design_complexity_score`**
    - **Raw Variables**: `armGroups_count`, `interventions_count`, `outcomeMeasures_count`
    - **Calculation**: Sum of all three counts

13. **`intervention_complexity_score`**
    - **Raw Variables**: `interventions_count`
    - **Calculation**: Direct copy

14. **`outcome_complexity_score`**
    - **Raw Variables**: `primaryOutcomes_count`, `secondaryOutcomes_count`
    - **Calculation**: primary_count + (secondary_count × 0.5)

15. **`masking_quality_category`**
    - **Raw Variables**: `designInfo_maskingInfo_masking`
    - **Calculation**: Text parsing for None/Single/Double/Triple/Quadruple

16. **`allocation_method`**
    - **Raw Variables**: `designInfo_allocation`
    - **Calculation**: Randomized vs Non-randomized classification

17. **`study_phase_category`**
    - **Raw Variables**: `phases`
    - **Calculation**: Parse for Phase 1, 2, 3, 4, combinations (1/2, 2/3, 3/4)

18. **`intervention_model_category`**
    - **Raw Variables**: `designInfo_interventionModel`
    - **Calculation**: Direct copy (PARALLEL, CROSSOVER, etc.)

19. **`primary_purpose_category`**
    - **Raw Variables**: `designInfo_primaryPurpose`
    - **Calculation**: Direct copy (TREATMENT, PREVENTION, etc.)

### D. PARTICIPANT FLOW META-VARIABLES
20. **`completion_rate`**
    - **Raw Variables**: `participantFlow_completed_total`, `participantFlow_started_total`
    - **Calculation**: (completed / started) × 100

21. **`dropout_rate`**
    - **Raw Variables**: `participantFlow_notCompleted_total`, `participantFlow_started_total`
    - **Calculation**: (not_completed / started) × 100

22. **`adverse_event_dropout_rate`**
    - **Raw Variables**: `participantFlow_dropouts_adverseEvent`, `participantFlow_started_total`
    - **Calculation**: (ae_dropouts / started) × 100

23. **`efficacy_dropout_rate`**
    - **Raw Variables**: `participantFlow_dropouts_lackEfficacy`, `participantFlow_started_total`
    - **Calculation**: (efficacy_dropouts / started) × 100

24. **`lost_followup_rate`**
    - **Raw Variables**: `participantFlow_dropouts_lostFollowup`, `participantFlow_started_total`
    - **Calculation**: (lost_followup / started) × 100

25. **`completion_rate_category`**
    - **Raw Variables**: `completion_rate` (derived)
    - **Calculation**: >90% = High, 80-90% = Medium, <80% = Low

26. **`dropout_pattern_category`**
    - **Raw Variables**: `adverse_event_dropout_rate`, `efficacy_dropout_rate` (both derived)
    - **Calculation**: >5% threshold for AE-driven/Efficacy-driven classification

### E. GEOGRAPHIC AND REGULATORY META-VARIABLES
27. **`number_of_countries`**
    - **Raw Variables**: `locations_countries`
    - **Calculation**: Count unique countries from list

28. **`number_of_locations`**
    - **Raw Variables**: `locations_count`
    - **Calculation**: Direct copy

29. **`locations_per_country`**
    - **Raw Variables**: `number_of_locations`, `number_of_countries` (both derived)
    - **Calculation**: locations / countries

30. **`geographic_scope_category`**
    - **Raw Variables**: `number_of_countries` (derived)
    - **Calculation**: 1 = Single-country, 2-5 = Regional, >5 = Global

31. **`regulatory_region_category`**
    - **Raw Variables**: `locations_countries`
    - **Calculation**: Analysis of country list for US-only/EU-only/Asia-Pacific/Global

32. **`fda_regulation_category`**
    - **Raw Variables**: `isFdaRegulatedDrug`, `isFdaRegulatedDevice`
    - **Calculation**: None/Drug/Device/Both classification

33. **`regulatory_oversight_category`**
    - **Raw Variables**: `oversightHasDmc`
    - **Calculation**: With DMC / Without DMC

### F. SPONSORSHIP AND FUNDING META-VARIABLES
34. **`sponsor_type_category`**
    - **Raw Variables**: `leadSponsor_class`
    - **Calculation**: Industry/Academic/Government/Other classification

35. **`collaboration_level_category`**
    - **Raw Variables**: `collaborators_count`
    - **Calculation**: 0 = Solo, 1-2 = Limited, >2 = Extensive

36. **`total_sponsor_entities`**
    - **Raw Variables**: `collaborators_count`
    - **Calculation**: 1 + collaborators_count

### G. OUTCOME AND EFFICACY META-VARIABLES
37. **`total_outcome_measures`**
    - **Raw Variables**: `primaryOutcomes_count`, `secondaryOutcomes_count`
    - **Calculation**: Sum of primary and secondary outcomes

38. **`primary_to_secondary_ratio`**
    - **Raw Variables**: `primaryOutcomes_count`, `secondaryOutcomes_count`
    - **Calculation**: primary_count / secondary_count

39. **`outcome_focus_category`**
    - **Raw Variables**: `primary_to_secondary_ratio` (derived)
    - **Calculation**: Ratio-based classification

### H. SAFETY AND ADVERSE EVENTS META-VARIABLES
40. **`adverse_events_reporting_completeness`**
    - **Raw Variables**: `adverseEvents_groups_count`, `adverseEvents_seriousEvents_count`, `adverseEvents_otherEvents_count`
    - **Calculation**: Composite completeness score (0-100%)

41. **`serious_ae_rate`**
    - **Raw Variables**: Placeholder for future implementation
    - **Calculation**: Currently set to NA

42. **`total_ae_rate`**
    - **Raw Variables**: Placeholder for future implementation
    - **Calculation**: Currently set to NA

43. **`safety_reporting_category`**
    - **Raw Variables**: `adverse_events_reporting_completeness` (derived)
    - **Calculation**: >80% = Complete, 40-80% = Partial, <40% = Minimal

### I. DATA COMPLETENESS AND QUALITY META-VARIABLES
44. **`protocol_completeness_score`**
    - **Raw Variables**: 15 key protocol variables (see detailed list below)
    - **Calculation**: (available_variables / 15) × 100

45. **`results_completeness_score`**
    - **Raw Variables**: 7 key results variables (see detailed list below)
    - **Calculation**: (available_variables / 7) × 100

46. **`overall_data_completeness_score`**
    - **Raw Variables**: All 22 protocol + results variables
    - **Calculation**: (available_variables / 22) × 100

47. **`data_quality_category`**
    - **Raw Variables**: `overall_data_completeness_score` (derived)
    - **Calculation**: >90% = >90%, 70-90% = 70-90%, <70% = <70%

48. **`results_availability_category`**
    - **Raw Variables**: `results_completeness_score` (derived)
    - **Calculation**: >80% = >80%, 40-80% = 40-80%, <40% = <40%

### J. STUDY CHARACTERISTICS FOR SUBGROUP ANALYSIS
49. **`enrollment_size_tertiles`**
    - **Raw Variables**: `baseline_total_participants`
    - **Calculation**: <200 = Small, 200-600 = Medium, >600 = Large

50. **`study_duration_tertiles`**
    - **Raw Variables**: `study_duration_months` (derived)
    - **Calculation**: <12 = Short, 12-24 = Medium, >24 = Long

51. **`study_era_category`**
    - **Raw Variables**: `startDateStruct_date`
    - **Calculation**: Extract year, <2010 = Early, 2010-2015 = Middle, >2015 = Recent

52. **`masking_effectiveness_category`**
    - **Raw Variables**: `masking_quality_category` (derived)
    - **Calculation**: Double/Triple/Quadruple = Effective, Single = Limited, None = None

---

## DETAILED COMPOSITE VARIABLE DEFINITIONS

### Protocol Completeness Score (15 key variables)
The protocol completeness score is calculated based on availability of these 15 key protocol variables:
1. `nctId` - NCT identifier
2. `organization_fullName` - Organization name
3. `briefTitle` - Study title
4. `overallStatus` - Study status
5. `studyType` - Study type
6. `phases` - Study phases
7. `designInfo_allocation` - Allocation method
8. `armGroups_count` - Number of arms
9. `conditions` - Study conditions
10. `healthyVolunteers` - Healthy volunteers
11. `locations_count` - Number of locations
12. `primaryOutcomes_count` - Primary outcomes count
13. `leadSponsor_name` - Lead sponsor
14. `oversightHasDmc` - DMC oversight
15. `briefSummary` - Brief summary

### Results Completeness Score (7 key variables)
The results completeness score is calculated based on availability of these 7 key results variables:
1. `baseline_total_participants` - Total participants
2. `baseline_age_mean` - Mean age
3. `baseline_sex_male_count` - Male count
4. `participantFlow_started_total` - Started participants
5. `participantFlow_completed_total` - Completed participants
6. `outcomeMeasures_count` - Outcome measures
7. `adverseEvents_groups_count` - AE groups

---

## TABLE 1: CHARACTERISTICS OF INCLUDED STUDIES (20 Variables)

### Study Design (5 variables)
1. **`study_id`** - Study identifier
2. **`study_phase_category`** - Study phase
3. **`allocation_method`** - Randomization method
4. **`masking_quality_category`** - Blinding quality
5. **`study_duration_months`** - Study duration

### Population & Baseline (4 variables)
6. **`baseline_total_participants`** - Sample size
7. **`baseline_age_mean_numeric`** - Mean age
8. **`male_percentage`** - Gender distribution
9. **`conditions`** - Primary conditions

### Interventions & Outcomes (3 variables)
10. **`armGroups_count`** - Number of arms
11. **`interventions_names`** - Intervention details
12. **`primaryOutcomes_count`** - Primary outcomes

### Study Conduct (2 variables)
13. **`completion_rate`** - Completion rate
14. **`dropout_rate`** - Dropout rate

### Geographic & Regulatory (2 variables)
15. **`number_of_countries`** - Geographic scope
16. **`geographic_scope_category`** - Scope category

### Sponsorship (2 variables)
17. **`sponsor_type_category`** - Sponsor type
18. **`leadSponsor_name`** - Lead sponsor

### Data Quality (2 variables)
19. **`data_quality_category`** - Data quality
20. **`results_availability_category`** - Results availability

---

## CATEGORICAL vs CONTINUOUS VARIABLES

### Categorical Variables (25 variables)
Used for subgroup analysis in meta-analysis:
- All category variables ending in "_category"
- All tertile variables ending in "_tertiles"
- Allocation method, phases, intervention model, primary purpose

### Continuous Variables (29 variables)
Used for meta-regression analysis:
- All duration variables in months
- All percentage variables
- All count variables
- All score variables

---

## VARIABLE CLASSIFICATION FOR META-ANALYSIS

### Variables for Subgroup Analysis (Categorical - 25 variables)
**File**: `all_categorical_variables.csv`

#### Study Design Subgroups
- `study_phase_category` - Phase 1, 2, 3, 4, combinations
- `allocation_method` - Randomized vs Non-randomized
- `masking_quality_category` - None, Single, Double, Triple, Quadruple
- `intervention_model_category` - Parallel, Crossover, Sequential
- `primary_purpose_category` - Treatment, Prevention, Diagnostic
- `masking_effectiveness_category` - Effective, Limited, None

#### Population Subgroups
- `age_category` - Young, Middle, Older
- `sample_size_category` - Small, Medium, Large
- `gender_balance_category` - Male-dominated, Balanced, Female-dominated
- `enrollment_size_tertiles` - Small, Medium, Large (different thresholds)

#### Geographic and Regulatory Subgroups
- `geographic_scope_category` - Single-country, Regional, Global
- `regulatory_region_category` - US-only, EU-only, Asia-Pacific, Global
- `fda_regulation_category` - None, Drug, Device, Both
- `regulatory_oversight_category` - With DMC, Without DMC

#### Temporal Subgroups
- `study_era_category` - Early, Middle, Recent
- `study_duration_tertiles` - Short, Medium, Long

#### Quality and Completeness Subgroups
- `completion_rate_category` - High, Medium, Low
- `data_quality_category` - >90%, 70-90%, <70%
- `results_availability_category` - >80%, 40-80%, <40%
- `safety_reporting_category` - Complete, Partial, Minimal

#### Sponsorship Subgroups
- `sponsor_type_category` - Industry, Academic, Government, Other
- `collaboration_level_category` - Solo, Limited, Extensive

#### Outcome and Safety Subgroups
- `outcome_focus_category` - Primary-focused, Balanced, Secondary-heavy
- `dropout_pattern_category` - AE-driven, Efficacy-driven, Mixed, Other

### Variables for Meta-Regression Analysis (Continuous - 29 variables)
**File**: `all_continuous_variables.csv`

#### Effect Modifiers
- `baseline_age_mean_numeric` - Age as continuous moderator
- `male_percentage` - Gender composition effect
- `study_duration_months` - Duration-response relationships
- `completion_rate` - Study quality indicator
- `design_complexity_score` - Complex vs simple designs

#### Study Quality Moderators
- `dropout_rate` - Quality indicator
- `adverse_event_dropout_rate` - Safety-related quality
- `protocol_completeness_score` - Data completeness
- `results_completeness_score` - Results reporting quality
- `overall_data_completeness_score` - Overall quality metric

#### Design and Methodology Moderators
- `intervention_complexity_score` - Intervention complexity
- `outcome_complexity_score` - Outcome burden
- `total_outcome_measures` - Multiple testing considerations
- `primary_to_secondary_ratio` - Endpoint focus

#### Geographic and Scale Moderators
- `number_of_countries` - International scope
- `number_of_locations` - Multi-center effects
- `locations_per_country` - Study density
- `total_sponsor_entities` - Collaboration level

#### Temporal Moderators
- `time_to_results_posting_months` - Publication bias
- `regulatory_review_time_months` - Regulatory efficiency
- `primary_completion_duration_months` - Execution efficiency

#### Safety and Tolerability Moderators
- `adverse_events_reporting_completeness` - Safety reporting quality
- `lost_followup_rate` - Study retention
- `efficacy_dropout_rate` - Treatment tolerability

#### Demographic Moderators
- `baseline_age_sd_numeric` - Age heterogeneity
- `female_percentage` - Gender distribution
- `serious_ae_rate` - Safety profile (placeholder)
- `total_ae_rate` - Overall safety (placeholder)

---

## HETEROGENEITY INVESTIGATION FRAMEWORK

### Clinical Heterogeneity Sources
**Population Factors**
- Age: `age_category`, `baseline_age_mean_numeric`
- Gender: `gender_balance_category`, `male_percentage`
- Sample size: `sample_size_category`, `baseline_total_participants`

**Intervention Factors**
- Complexity: `intervention_complexity_score`, `intervention_model_category`
- Phase: `study_phase_category`

**Outcome Factors**
- Focus: `outcome_focus_category`, `primary_to_secondary_ratio`
- Burden: `total_outcome_measures`, `outcome_complexity_score`

**Setting Factors**
- Geographic: `geographic_scope_category`, `regulatory_region_category`
- Era: `study_era_category`, `study_duration_months`

### Methodological Heterogeneity Sources
**Design Quality**
- Masking: `masking_quality_category`, `masking_effectiveness_category`
- Allocation: `allocation_method`

**Study Conduct**
- Completion: `completion_rate_category`, `completion_rate`
- Dropouts: `dropout_pattern_category`, `dropout_rate`

**Data Quality**
- Completeness: `data_quality_category`, `overall_data_completeness_score`
- Reporting: `results_availability_category`, `safety_reporting_category`

### Statistical Heterogeneity Predictors
**Study Size Effects**
- Size categories: `enrollment_size_tertiles`, `sample_size_category`
- Precision: `baseline_total_participants`

**Quality Indicators**
- Completion: `completion_rate`, `completion_rate_category`
- Data quality: `data_quality_category`, `protocol_completeness_score`

**Design Factors**
- Complexity: `design_complexity_score`
- Masking: `masking_effectiveness_category`

**Temporal Factors**
- Era: `study_era_category`
- Duration: `study_duration_tertiles`, `study_duration_months`

---

## PUBLICATION-READY TABLE 1 VARIABLES

### Essential Characteristics (20 variables)
**File**: `characteristics_of_included_studies.csv`

#### Study Identification (1 variable)
1. `study_id` - NCT identifier

#### Study Design (4 variables)
2. `study_phase_category` - Study phase
3. `allocation_method` - Randomization
4. `masking_quality_category` - Blinding quality
5. `study_duration_months` - Duration

#### Population Characteristics (3 variables)
6. `baseline_total_participants` - Sample size
7. `baseline_age_mean_numeric` - Mean age
8. `male_percentage` - Gender distribution
9. `conditions` - Primary conditions

#### Interventions and Outcomes (3 variables)
10. `armGroups_count` - Number of arms
11. `interventions_names` - Interventions
12. `primaryOutcomes_count` - Primary outcomes

#### Study Conduct (2 variables)
13. `completion_rate` - Completion rate
14. `dropout_rate` - Dropout rate

#### Geographic Scope (2 variables)
15. `number_of_countries` - Countries
16. `geographic_scope_category` - Scope

#### Sponsorship (2 variables)
17. `sponsor_type_category` - Sponsor type
18. `leadSponsor_name` - Lead sponsor

#### Data Quality (2 variables)
19. `data_quality_category` - Data quality
20. `results_availability_category` - Results availability

### Summary Statistics Format
- **Continuous variables**: Mean ± SD format
- **Categorical variables**: Most common (n/total) format
- **Text variables**: "Multiple (X different)" or actual value
- **First row**: Summary across all studies
- **Subsequent rows**: Individual study data

---

## THRESHOLD DEFINITIONS AND RATIONALE

### Age Categories
- **Young (<50 years)**: Pre-middle age population
- **Middle (50-65 years)**: Middle-aged population
- **Older (>65 years)**: Elderly population
- **Rationale**: Standard age stratification for clinical research

### Sample Size Categories
- **Small (<100 participants)**: Pilot/feasibility studies
- **Medium (100-500 participants)**: Standard efficacy studies
- **Large (>500 participants)**: Large-scale effectiveness studies
- **Rationale**: Common clinical trial size classifications

### Completion Rate Categories
- **High (>90%)**: Excellent retention
- **Medium (80-90%)**: Good retention
- **Low (<80%)**: Poor retention
- **Rationale**: Clinical trial quality benchmarks

### Data Quality Categories
- **>90%**: High-quality, comprehensive data
- **70-90%**: Medium-quality, mostly complete data
- **<70%**: Low-quality, incomplete data
- **Rationale**: Data completeness standards for meta-analysis

### Geographic Scope Categories
- **Single-country**: National studies
- **Regional (2-5 countries)**: Regional studies
- **Global (>5 countries)**: International studies
- **Rationale**: Regulatory and generalizability considerations

### Study Duration Tertiles
- **Short (<12 months)**: Short-term studies
- **Medium (12-24 months)**: Medium-term studies
- **Long (>24 months)**: Long-term studies
- **Rationale**: Clinical meaningfulness of follow-up periods

### Enrollment Size Tertiles
- **Small (<200)**: Small studies
- **Medium (200-600)**: Medium studies
- **Large (>600)**: Large studies
- **Rationale**: Statistical power and precision considerations

---

## USAGE GUIDELINES

### For Systematic Reviews
1. **Study Selection**: Use `data_quality_category` and `results_availability_category`
2. **Risk of Bias**: Use `masking_quality_category`, `allocation_method`, `completion_rate_category`
3. **Study Characteristics**: Use all 20 Table 1 variables
4. **PRISMA Reporting**: Use geographic and temporal variables

### For Meta-Analysis
1. **Effect Size Calculation**: Use raw variables for outcome data
2. **Subgroup Analysis**: Use all 25 categorical variables
3. **Meta-Regression**: Use all 29 continuous variables
4. **Heterogeneity Investigation**: Use clinical, methodological, and statistical predictors
5. **Sensitivity Analysis**: Use quality indicators and study characteristics

### For Network Meta-Analysis
1. **Node Definition**: Use `interventions_names` and `conditions`
2. **Study Characteristics**: Use design and population variables
3. **Transitivity Assessment**: Use all demographic and design variables
4. **Inconsistency Evaluation**: Use methodological quality variables

### Missing Data Handling
- **Strategy**: Calculate what's possible first, set to "NA" only when all required inputs are "NA"
- **Rationale**: Maximizes data utilization while maintaining integrity
- **Implementation**: Safe conversion functions with fallback logic

### File Formats
- **All CSV files are transposed**: Variables as rows, studies as columns
- **UTF-8 encoding**: Ensures proper character display (±, etc.)
- **Summary statistics**: Included in characteristics table only
- **Identifier**: `study_id` included in all files

### Quality Assurance
- **Data Validation**: Check for logical inconsistencies
- **Completeness Assessment**: Use completeness scores
- **Outlier Detection**: Use continuous variables for outlier identification
- **Bias Assessment**: Use quality and reporting variables

---

## PRACTICAL EXAMPLES

### Example 1: Subgroup Analysis Setup
```r
# Load categorical variables
categorical_data <- read.csv("all_categorical_variables.csv", row.names=1)

# Subgroup by study phase
phase_subgroups <- categorical_data["study_phase_category", ]

# Subgroup by masking quality
masking_subgroups <- categorical_data["masking_quality_category", ]

# Subgroup by sponsor type
sponsor_subgroups <- categorical_data["sponsor_type_category", ]
```

### Example 2: Meta-Regression Setup
```r
# Load continuous variables
continuous_data <- read.csv("all_continuous_variables.csv", row.names=1)

# Age as moderator
age_moderator <- as.numeric(continuous_data["baseline_age_mean_numeric", ])

# Study duration as moderator
duration_moderator <- as.numeric(continuous_data["study_duration_months", ])

# Sample size as moderator
sample_size_moderator <- as.numeric(continuous_data["baseline_total_participants", ])
```

### Example 3: Quality Assessment
```r
# Load characteristics table
characteristics <- read.csv("characteristics_of_included_studies.csv", row.names=1)

# Data quality assessment
data_quality <- characteristics["data_quality_category", ]
results_availability <- characteristics["results_availability_category", ]
completion_rates <- as.numeric(characteristics["completion_rate", ])

# Risk of bias indicators
masking_quality <- characteristics["masking_quality_category", ]
allocation_method <- characteristics["allocation_method", ]
```

---

## TROUBLESHOOTING

### Common Issues and Solutions

#### Issue: "NA" Values in Calculations
**Cause**: Missing raw data for calculation
**Solution**: Check raw variables file for data availability
**Prevention**: Use completeness scores to assess data quality

#### Issue: Inconsistent Category Labels
**Cause**: Text parsing variations in raw data
**Solution**: Check raw variables for exact text format
**Prevention**: Use standardized category mappings

#### Issue: Date Calculation Errors
**Cause**: Missing or invalid date formats
**Solution**: Check date fields in raw variables
**Prevention**: Validate date formats before calculation

#### Issue: Percentage Calculations >100%
**Cause**: Inconsistent participant counts
**Solution**: Check participant flow variables for consistency
**Prevention**: Validate participant flow logic

### Data Quality Checks

#### Essential Validations
1. **Participant Flow Consistency**
   - `participantFlow_started_total` ≥ `participantFlow_completed_total`
   - Sum of dropouts ≤ `participantFlow_notCompleted_total`

2. **Date Logic**
   - `startDateStruct_date` < `completionDateStruct_date`
   - `completionDateStruct_date` < `resultsFirstPostDateStruct_date`

3. **Percentage Bounds**
   - All percentage variables between 0-100%
   - `male_percentage` + `female_percentage` ≈ 100% (when both available)

4. **Count Consistency**
   - `baseline_sex_male_count` + `baseline_sex_female_count` ≤ `baseline_total_participants`
   - `primaryOutcomes_count` + `secondaryOutcomes_count` = `total_outcome_measures`

### Variable Selection Guidelines

#### For Primary Analysis
**Essential Variables**: Study design, population, interventions, outcomes
**Files**: `characteristics_of_included_studies.csv`
**Focus**: 20 most critical variables

#### For Subgroup Analysis
**Essential Variables**: All categorical moderators
**Files**: `all_categorical_variables.csv`
**Focus**: 25 categorical variables for subgroup exploration

#### For Meta-Regression
**Essential Variables**: All continuous moderators
**Files**: `all_continuous_variables.csv`
**Focus**: 29 continuous variables for dose-response analysis

#### For Sensitivity Analysis
**Essential Variables**: Quality indicators and study characteristics
**Files**: All files combined
**Focus**: Quality, completeness, and bias indicators

---

## VARIABLE HIERARCHY

### Level 1: Raw Variables (122 variables)
- **Source**: Direct extraction from JSON modules
- **Purpose**: Foundation data for all analyses
- **Usage**: Detailed investigation, data validation
- **File**: `raw_variables.csv`

### Level 2: Meta-Variables (52 variables)
- **Source**: Calculated from raw variables
- **Purpose**: Meta-analysis ready variables
- **Usage**: Subgroup analysis, meta-regression
- **Files**: `meta_variables.csv`, `all_categorical_variables.csv`, `all_continuous_variables.csv`

### Level 3: Characteristics (20 variables)
- **Source**: Selected from meta-variables
- **Purpose**: Publication-ready summary
- **Usage**: Table 1, study description
- **File**: `characteristics_of_included_studies.csv`

---

## FUTURE ENHANCEMENTS

### Planned Additions
1. **Safety Variables**: `serious_ae_rate`, `total_ae_rate` (currently placeholders)
2. **Efficacy Variables**: Treatment effect sizes and confidence intervals
3. **Economic Variables**: Cost-effectiveness and resource utilization
4. **Patient-Reported Variables**: Quality of life and patient satisfaction

### Expansion Opportunities
1. **Additional Modules**: IPD sharing, expanded access, regulatory submissions
2. **Derived Metrics**: Network connectivity, similarity indices
3. **Quality Scores**: Composite risk of bias scores
4. **Temporal Trends**: Publication patterns, regulatory changes

---

## CITATION AND ACKNOWLEDGMENTS

### Recommended Citation
When using this variable system in publications, please cite:
"Clinical trials variables extracted using comprehensive JSON module parsing with 122 raw variables, 52 meta-variables, and 20 publication-ready characteristics as described in README_variables.md"

### Variable System Version
- **Version**: 1.0
- **Last Updated**: 2025-01-20
- **Compatibility**: Clinical trials data from ClinicalTrials.gov API
- **Standards**: Follows CONSORT, PRISMA, and Cochrane guidelines

### Contact Information
For questions about variable definitions, calculations, or usage:
- **Technical Issues**: Check troubleshooting section
- **Methodological Questions**: Refer to rationale sections
- **Enhancement Requests**: Document in future enhancements section

---

*This README serves as the comprehensive reference for all clinical trials variables. All questions regarding raw variables, meta-variables, subgroup analysis variables, meta-regression variables, and their relationships should be answered using this documentation.*
