#!/usr/bin/env python3
"""
Clinical Trials Meta-Analysis System
====================================

This module provides comprehensive meta-analysis of clinical trial data with:
- Fast text-based term discovery for simple searches
- Multi-keyword search with AND logic
- AI-powered analysis for complex criteria using user-selected models
- Progressive filtering with consistent discovery-to-filtering logic

Key Functions:
- discover_terms_in_section(): Find related terms in study sections
- filter_studies_by_criteria(): Progressive filtering with text matching
- filter_studies_by_criteria_ai(): AI-powered filtering with LLM analysis
- analyze_studies(): Full AI analysis of study datasets

Author: Clinical Trials Analysis System
Version: 2.0 - Clean Architecture
"""

# Standard library imports
import argparse
import csv
import json
import os
import re
import sys
from collections import defaultdict, Counter
from pathlib import Path
from datetime import datetime

# Local imports
from ai_model_setup import query_ollama
from meta_analysis_table1 import (
    extract_raw_variables_from_json,
    write_raw_variables_csv,
    generate_complete_table1,
    extract_identification_module_vars,
    extract_status_module_vars,
    extract_design_module_vars,
    extract_arms_interventions_module_vars,
    extract_conditions_module_vars,
    extract_eligibility_module_vars,
    extract_contacts_locations_module_vars,
    extract_outcomes_module_vars,
    extract_sponsor_collaborators_module_vars,
    extract_oversight_module_vars,
    extract_description_module_vars,
    extract_references_module_vars,
    extract_baseline_characteristics_module_vars,
    extract_participant_flow_module_vars,
    extract_outcome_measures_module_vars,
    extract_adverse_events_module_vars,
    extract_more_info_module_vars,
    extract_derived_section_vars
)

# Constants
MIN_TERM_LENGTH = 3
MAX_TERMS_RETURNED = 50
DEFAULT_MIN_FREQUENCY = 2
AI_ANALYSIS_CHUNK_SIZE = 10
SEARCH_CONTEXT_WORDS = 5

def get_valid_sections():
    """
    Get list of all valid section names for validation

    Returns:
        list: List of valid section names
    """
    return [
        # Legacy UI names
        'trial_description', 'recruitment', 'outcome_measures', 'adverse_events',
        # Protocol Section modules
        'protocolSection_identificationModule', 'protocolSection_statusModule',
        'protocolSection_oversightModule', 'protocolSection_descriptionModule',
        'protocolSection_conditionsModule', 'protocolSection_designModule',
        'protocolSection_armsInterventionsModule', 'protocolSection_outcomesModule',
        'protocolSection_eligibilityModule', 'protocolSection_contactsLocationsModule',
        'protocolSection_referencesModule', 'protocolSection_ipdSharingStatementModule',
        'protocolSection_sponsorCollaboratorsModule', 'protocolSection_largeDocumentModule',
        # Results Section modules
        'resultsSection_participantFlowModule', 'resultsSection_baselineCharacteristicsModule',
        'resultsSection_outcomeMeasuresModule', 'resultsSection_adverseEventsModule',
        'resultsSection_certainAgreementsModule', 'resultsSection_limitationsAndCaveatsModule',
        'resultsSection_moreInfoModule', 'resultsSection_pointOfContactModule',
        # Derived Section modules
        'derivedSection_derivedSection', 'derivedSection_documentSection',
        'derivedSection_miscInfoModule'
    ]

def get_text_file_for_section(section):
    """
    Map UI section names to corresponding module text files

    Args:
        section (str): Module name (e.g., 'protocolSection_identificationModule', 'resultsSection_adverseEventsModule')

    Returns:
        str: Text filename to search in
    """
    return f"{section}.txt"

def validate_section(section):
    """
    Validate that the section name is supported

    Args:
        section (str): Section name to validate

    Returns:
        tuple: (is_valid, error_message)
    """
    valid_sections = get_valid_sections()
    if section not in valid_sections:
        return False, f'Invalid section: {section}. Must be one of the supported module sections.'
    return True, None

def get_study_directories(studies_dir, filtered_studies=None):
    """
    Get list of study directories to search

    Args:
        studies_dir (str): Base studies directory
        filtered_studies (list, optional): List of study IDs to filter by

    Returns:
        tuple: (study_dirs, error_message)
    """
    if not os.path.exists(studies_dir):
        return None, f'Studies directory not found: {studies_dir}'

    study_dirs = [d for d in os.listdir(studies_dir)
                  if os.path.isdir(os.path.join(studies_dir, d)) and d.startswith('NCT')]

    if filtered_studies:
        study_dirs = [d for d in study_dirs if d in filtered_studies]

    return study_dirs, None

def discover_terms_in_section(studies_dir, section, search_term, filtered_studies=None, use_ai=False, llm_model=None):
    """
    Discover related terms in a specific section of clinical studies.

    This function provides intelligent term discovery with two modes:

    1. **Fast Text Matching** (Default):
       - Examples: "diabetes", "arrhythmia", "canagliflozin"
       - Uses simple text search and word frequency analysis
       - Returns instantly with related terms

    2. **AI-Powered Discovery** (use_ai=True):
       - Uses LLM to analyze study content and find contextually related terms
       - More accurate and contextually relevant results
       - Requires llm_model parameter

    Args:
        studies_dir (str): Directory containing study folders
        section (str): Section to search (module names like 'resultsSection_adverseEventsModule')
        search_term (str): Term to search for
        filtered_studies (list, optional): List of study IDs to search within
        use_ai (bool): Whether to use AI-powered term discovery
        llm_model (str): LLM model to use for AI analysis (required if use_ai=True)

    Returns:
        dict: Discovery results with related terms and study counts
    """
    print(f"🔍 Discovering terms for '{search_term}' in {section}")
    if use_ai and llm_model:
        print(f"🤖 Using AI-powered discovery with {llm_model}")
    else:
        print(f"⚡ Using fast text matching")

    # Validate section
    is_valid, error_msg = validate_section(section)
    if not is_valid:
        return {'success': False, 'error': error_msg}

    # Get study directories
    study_dirs, error_msg = get_study_directories(studies_dir, filtered_studies)
    if error_msg:
        return {'success': False, 'error': error_msg}

    # Get the text file to search based on section
    text_filename = get_text_file_for_section(section)

    print(f"📁 Searching {len(study_dirs)} studies in {text_filename}")

    # Choose discovery method
    if use_ai and llm_model:
        return _discover_terms_ai(study_dirs, studies_dir, text_filename, section, search_term, llm_model)
    else:
        return _discover_terms_text_matching(study_dirs, studies_dir, text_filename, section, search_term)

def _read_study_text_file(study_path, text_filename):
    """
    Read text content from a study file

    Args:
        study_path (str): Path to study directory
        text_filename (str): Name of text file to read

    Returns:
        str or None: Text content or None if error
    """
    text_file = os.path.join(study_path, text_filename)
    if not os.path.exists(text_file):
        return None

    try:
        with open(text_file, 'r', encoding='utf-8') as f:
            return f.read().lower()
    except Exception as e:
        print(f"⚠️ Error reading {text_file}: {e}")
        return None

def _extract_meaningful_words(text_content):
    """
    Extract meaningful words from text content

    Args:
        text_content (str): Text to process

    Returns:
        list: List of meaningful words
    """
    words = re.findall(r'\b\w+\b', text_content)
    return [word for word in words
            if len(word) >= MIN_TERM_LENGTH and word.isalpha()]

def _calculate_term_study_counts(terms, studies_with_term, studies_dir, text_filename):
    """
    Calculate how many studies contain each term

    Args:
        terms (list): List of terms to count
        studies_with_term (list): List of study directories that contain the search term
        studies_dir (str): Base studies directory
        text_filename (str): Text file to search in

    Returns:
        dict: Term to study count mapping
    """
    term_study_counts = {}

    # Cache study contents to avoid re-reading files
    study_contents = {}
    for study_dir in studies_with_term:
        study_path = os.path.join(studies_dir, study_dir)
        content = _read_study_text_file(study_path, text_filename)
        if content:
            study_contents[study_dir] = content

    # Count occurrences of each term
    for term in terms:
        count = sum(1 for content in study_contents.values() if term in content)
        if count > 0:
            term_study_counts[term] = count

    return term_study_counts

def _discover_terms_text_matching(study_dirs, studies_dir, text_filename, section, search_term):
    """
    Fast text-based term discovery using word frequency analysis

    Args:
        study_dirs (list): List of study directories to search
        studies_dir (str): Base studies directory
        text_filename (str): Text file to search in
        section (str): Section being searched
        search_term (str): Term to search for

    Returns:
        dict: Discovery results with related terms and study counts
    """
    term_counts = Counter()
    studies_with_term = []
    search_term_lower = search_term.lower()

    # First pass: find studies with search term and collect word frequencies
    for study_dir in study_dirs:
        study_path = os.path.join(studies_dir, study_dir)
        text_content = _read_study_text_file(study_path, text_filename)

        if not text_content:
            continue

        # Check if search term is present
        if search_term_lower in text_content:
            studies_with_term.append(study_dir)

            # Extract and count meaningful words
            words = _extract_meaningful_words(text_content)
            for word in words:
                term_counts[word] += 1

    # Get top terms (excluding search term)
    top_terms = [term for term, count in term_counts.most_common(MAX_TERMS_RETURNED)
                 if term != search_term_lower and count >= DEFAULT_MIN_FREQUENCY]

    # Calculate study counts for top terms
    term_study_counts = _calculate_term_study_counts(
        top_terms, studies_with_term, studies_dir, text_filename
    )

    # Format related terms with study counts
    related_terms = []
    for term in top_terms:
        if term in term_study_counts:
            count = term_study_counts[term]
            studies_text = "study" if count == 1 else "studies"
            related_terms.append(f"{term} ({count} {studies_text})")

    return {
        'success': True,
        'search_term': search_term,
        'section': section,
        'studies_found': len(studies_with_term),
        'total_studies_searched': len(study_dirs),
        'related_terms': related_terms[:10],  # Top 10 related terms with counts
        'matching_studies': studies_with_term,
        'method': 'text_matching'
    }

def _create_ai_analysis_prompt(search_term, section, text_content):
    """
    Create prompt for AI analysis of clinical trial data

    Args:
        search_term (str): Term to find related terms for
        section (str): Section being analyzed
        text_content (str): Text content to analyze

    Returns:
        str: Formatted prompt for AI analysis
    """
    # Limit content to avoid token limits
    limited_content = text_content[:3000]

    return f"""
Analyze this clinical trial data and find terms that are contextually related to "{search_term}".

Study Data from {section}:
{limited_content}

Please identify medical terms, conditions, symptoms, treatments, or outcomes that are related to "{search_term}".
Return only a comma-separated list of related terms (maximum 10 terms), no explanations.
Focus on clinically relevant terms that would be useful for medical research.

Example format: term1, term2, term3, term4
"""

def _process_ai_response(ai_response, search_term, text_content):
    """
    Process AI response to extract and validate terms

    Args:
        ai_response (str): Raw AI response
        search_term (str): Original search term
        text_content (str): Text content to validate against

    Returns:
        list: List of validated terms
    """
    if not ai_response:
        return []

    # Parse AI response to extract terms
    terms = [term.strip().lower() for term in ai_response.split(',') if term.strip()]
    validated_terms = []

    for term in terms[:10]:  # Limit to 10 terms per study
        if not term or len(term) <= 2 or term == search_term.lower():
            continue

        # Normalize term formatting
        normalized_term = term.replace('_', ' ').replace('  ', ' ').strip()

        # Check if the normalized term actually appears in the text
        if normalized_term in text_content.lower():
            validated_terms.append(normalized_term)

    return validated_terms

def _discover_terms_ai(study_dirs, studies_dir, text_filename, section, search_term, llm_model):
    """
    AI-powered term discovery using LLM analysis

    Args:
        study_dirs (list): List of study directories to search
        studies_dir (str): Base studies directory
        text_filename (str): Text file to search in
        section (str): Section being searched
        search_term (str): Term to search for
        llm_model (str): LLM model to use for analysis

    Returns:
        dict: Discovery results with AI-generated related terms
    """
    studies_with_term = []
    related_terms_with_counts = Counter()
    search_term_lower = search_term.lower()

    print(f"🤖 Using AI model {llm_model} for contextual term discovery")

    # Process studies in chunks to avoid overwhelming the AI
    for i, study_dir in enumerate(study_dirs):
        if i % AI_ANALYSIS_CHUNK_SIZE == 0:
            print(f"📊 Processing studies {i+1}-{min(i+AI_ANALYSIS_CHUNK_SIZE, len(study_dirs))} of {len(study_dirs)}")

        study_path = os.path.join(studies_dir, study_dir)
        text_content = _read_study_text_file(study_path, text_filename)

        if not text_content:
            continue

        # Check if search term is present (basic check first)
        if search_term_lower not in text_content:
            continue

        studies_with_term.append(study_dir)

        # Use AI to find related terms
        prompt = _create_ai_analysis_prompt(search_term, section, text_content)
        response = query_ollama(prompt, llm_model)

        if response.get('success') and response.get('response'):
            ai_terms = response.get('response', '').strip()
            validated_terms = _process_ai_response(ai_terms, search_term, text_content)

            # Count validated terms
            for term in validated_terms:
                related_terms_with_counts[term] += 1
        else:
            print(f"⚠️ AI analysis failed for {study_dir}: {response.get('error', 'Unknown error')}")

    # Validate AI-discovered terms across all studies
    related_terms = _validate_ai_terms(
        related_terms_with_counts, study_dirs, studies_dir, text_filename
    )

    return {
        'success': True,
        'search_term': search_term,
        'section': section,
        'studies_found': len(studies_with_term),
        'total_studies_searched': len(study_dirs),
        'related_terms': related_terms,
        'matching_studies': studies_with_term,
        'method': 'ai_powered',
        'ai_model_used': llm_model
    }

def _validate_ai_terms(related_terms_with_counts, study_dirs, studies_dir, text_filename):
    """
    Validate AI-discovered terms by counting their actual occurrences across all studies

    Args:
        related_terms_with_counts (Counter): AI-discovered terms with counts
        study_dirs (list): List of study directories
        studies_dir (str): Base studies directory
        text_filename (str): Text file to search in

    Returns:
        list: List of validated terms with actual study counts
    """
    related_terms = []

    for term, ai_count in related_terms_with_counts.most_common(15):
        actual_count = 0

        # Count actual occurrences across all studies
        for study_dir in study_dirs:
            study_path = os.path.join(studies_dir, study_dir)
            text_content = _read_study_text_file(study_path, text_filename)

            if not text_content:
                continue

            # Try multiple variations to handle formatting differences
            term_variations = [
                term.lower(),
                term.replace('_', ' ').lower(),
                term.replace(' ', '_').lower()
            ]

            if any(variation in text_content for variation in term_variations):
                actual_count += 1

        # Only include terms that actually appear in the text
        if actual_count > 0:
            studies_text = "study" if actual_count == 1 else "studies"
            related_terms.append(f"{term} ({actual_count} {studies_text})")
            print(f"🔍 AI Discovery: '{term}' -> validated count: {actual_count} (AI suggested: {ai_count})")
        else:
            print(f"⚠️ AI Discovery: '{term}' -> not found in text (AI suggested: {ai_count})")

    return related_terms

def _create_detailed_terms_info(clean_terms, term_matches, term_study_counts, original_terms_with_counts):
    """
    Create detailed term information with actual study counts from filtering

    Args:
        clean_terms (list): List of clean terms
        term_matches (dict): Term to matching studies mapping
        term_study_counts (dict): Original study counts from discovery
        original_terms_with_counts (list): Original terms with counts

    Returns:
        list: List of detailed terms with actual counts
    """
    detailed_terms = []
    for clean_term in clean_terms:
        actual_count = len(term_matches[clean_term])
        studies_text = "study" if actual_count == 1 else "studies"
        detailed_terms.append(f"{clean_term} ({actual_count} {studies_text})")
    return detailed_terms

def _create_filter_step_info(step_num, criteria, detailed_terms, current_studies,
                           study_dirs, filter_steps, clean_terms, term_matches):
    """
    Create filter step information for tracking

    Args:
        step_num (int): Step number
        criteria (dict): Filter criteria
        detailed_terms (list): Detailed terms with counts
        current_studies (set): Current set of studies
        study_dirs (list): All study directories
        filter_steps (list): Previous filter steps
        clean_terms (list): Clean terms
        term_matches (dict): Term matches

    Returns:
        dict: Filter step information
    """
    # Create a copy of criteria with detailed terms for display
    display_criteria = criteria.copy()
    display_criteria['terms'] = detailed_terms
    display_criteria['term_logic'] = 'OR'  # Indicate OR logic is used

    # Determine step type for better display
    step_type = 'Primary Outcome' if step_num == 1 else f'Criteria {step_num - 1}'

    return {
        'step': step_num,
        'step_name': step_type,
        'criteria': display_criteria,
        'studies_remaining': len(current_studies),
        'studies_filtered_out': (len(study_dirs) - len(current_studies)
                               if step_num == 1
                               else filter_steps[-1]['studies_remaining'] - len(current_studies)),
        'unique_studies': len(current_studies),  # Total unique studies after OR logic
        'term_breakdown': {clean_term: len(term_matches[clean_term]) for clean_term in clean_terms}
    }

def _display_overlap_info(step_num, current_studies, clean_terms, term_matches, original_terms_with_counts):
    """
    Display overlap information for filtering step

    Args:
        step_num (int): Step number
        current_studies (set): Current set of studies
        clean_terms (list): Clean terms
        term_matches (dict): Term matches
        original_terms_with_counts (list): Original terms with counts
    """
    # Calculate and display overlap information
    total_individual_matches = sum(len(term_matches[term]) for term in clean_terms)
    unique_studies_count = len(current_studies)
    overlap_count = total_individual_matches - unique_studies_count

    print(f"📊 Step {step_num}: {unique_studies_count} unique studies remaining")
    print(f"   Individual term matches: {total_individual_matches}")
    print(f"   Overlapping studies: {overlap_count}")

    for term, studies in term_matches.items():
        # Find the original term with count from discovery
        original_term_with_count = next(
            (t for t in original_terms_with_counts if t.startswith(term)), ""
        )
        print(f"   - {term}: {len(studies)} studies (discovery showed: {original_term_with_count})")

def _clean_filter_terms(terms):
    """
    Clean filter terms by removing study counts in parentheses

    Args:
        terms (list): List of terms with potential study counts

    Returns:
        tuple: (clean_terms, original_terms_with_counts, term_study_counts)
    """
    clean_terms = []
    original_terms_with_counts = []
    term_study_counts = {}

    for term in terms:
        # Extract study count from parentheses - handle both "(6)" and "(6 studies)" formats
        count_match = re.search(r'\((\d+)(?:\s+studies?)?\)$', term.strip())
        study_count = int(count_match.group(1)) if count_match else 0

        # Remove study count in parentheses to get clean term
        clean_term = re.sub(r'\s*\(\d+(?:\s+studies?)?\)$', '', term.strip())
        clean_terms.append(clean_term)
        original_terms_with_counts.append(term.strip())
        term_study_counts[clean_term] = study_count

    return clean_terms, original_terms_with_counts, term_study_counts

def _get_term_variations(term):
    """
    Get different variations of a term to handle formatting differences

    Args:
        term (str): Original term

    Returns:
        list: List of term variations
    """
    return [
        term.lower(),
        term.replace('_', ' ').lower(),
        term.replace(' ', '_').lower()
    ]

def _filter_studies_by_terms(current_studies, clean_terms, studies_dir, text_filename):
    """
    Filter studies by checking if they contain any of the specified terms

    Args:
        current_studies (set): Current set of studies to filter
        clean_terms (list): List of clean terms to search for
        studies_dir (str): Base studies directory
        text_filename (str): Text file to search in

    Returns:
        tuple: (matching_studies, term_matches)
    """
    matching_studies = set()
    term_matches = {clean_term: set() for clean_term in clean_terms}

    for study_dir in current_studies:
        study_path = os.path.join(studies_dir, study_dir)
        text_content = _read_study_text_file(study_path, text_filename)

        if not text_content:
            continue

        # Check if ANY term is present (OR logic) and track which terms match
        study_matches_any_term = False
        for clean_term in clean_terms:
            term_variations = _get_term_variations(clean_term)

            for variation in term_variations:
                if variation in text_content:
                    term_matches[clean_term].add(study_dir)
                    study_matches_any_term = True
                    print(f"🎯 Filtering: Found '{variation}' in {study_dir} (original term: '{clean_term}')")
                    break

            if study_matches_any_term:
                break

        if study_matches_any_term:
            matching_studies.add(study_dir)

    return matching_studies, term_matches

def filter_studies_by_criteria(studies_dir, criteria_list):
    """
    Progressive filtering of studies using text-based matching

    Args:
        studies_dir (str): Directory containing study folders
        criteria_list (list): List of filter criteria

    Returns:
        dict: Filtering results with study counts at each step
    """
    print(f"🔍 Progressive filtering with {len(criteria_list)} criteria")

    # Get study directories
    study_dirs, error_msg = get_study_directories(studies_dir)
    if error_msg:
        return {'success': False, 'error': error_msg}

    current_studies = set(study_dirs)
    filter_steps = []

    print(f"📁 Starting with {len(current_studies)} studies")

    for i, criteria in enumerate(criteria_list, 1):
        section = criteria.get('section', 'trial_description')
        terms = criteria.get('terms', [])

        if not terms:
            continue

        # Clean terms and get metadata
        clean_terms, original_terms_with_counts, term_study_counts = _clean_filter_terms(terms)

        print(f"🔍 Step {i}: Filtering by {clean_terms} in {section} (OR logic)")

        # Get the text file to search based on section
        text_filename = get_text_file_for_section(section)

        # Filter studies by terms
        matching_studies, term_matches = _filter_studies_by_terms(
            current_studies, clean_terms, studies_dir, text_filename
        )

        # Update current studies
        current_studies = matching_studies

        # Create detailed term information with actual study counts from filtering
        detailed_terms = _create_detailed_terms_info(
            clean_terms, term_matches, term_study_counts, original_terms_with_counts
        )

        # Create filter step information
        filter_step = _create_filter_step_info(
            i, criteria, detailed_terms, current_studies, study_dirs,
            filter_steps, clean_terms, term_matches
        )
        filter_steps.append(filter_step)

        # Display overlap information
        _display_overlap_info(i, current_studies, clean_terms, term_matches, original_terms_with_counts)

        if not current_studies:
            print("⚠️ No studies remaining after filtering")
            break
    
    return {
        'success': True,
        'filtered_studies': list(current_studies),
        'filter_steps': filter_steps,
        'total_studies_initial': len(study_dirs),
        'total_studies_final': len(current_studies)
    }

def filter_studies_by_criteria_ai(studies_dir, criteria_list, llm_model):
    """
    Progressive filtering of studies using AI analysis

    Args:
        studies_dir (str): Directory containing study folders
        criteria_list (list): List of filter criteria
        llm_model (str): LLM model to use for analysis

    Returns:
        dict: AI filtering results with detailed analysis
    """
    print(f"🤖 AI-powered progressive filtering with {llm_model}")

    # Get study directories
    study_dirs, error_msg = get_study_directories(studies_dir)
    if error_msg:
        return {'success': False, 'error': error_msg}

    current_studies = set(study_dirs)
    filter_steps = []

    print(f"📁 Starting AI analysis with {len(current_studies)} studies")

    for i, criteria in enumerate(criteria_list, 1):
        section = criteria.get('section', 'trial_description')
        description = criteria.get('description', '')

        if not description:
            continue

        print(f"🤖 Step {i}: AI filtering by '{description}' in {section}")

        # Process studies with AI
        matching_studies = _ai_filter_studies(
            current_studies, studies_dir, section, description, llm_model
        )

        # Update current studies
        current_studies = matching_studies

        # Create filter step information
        filter_step = _create_ai_filter_step_info(
            i, criteria, current_studies, study_dirs, filter_steps
        )
        filter_steps.append(filter_step)

        print(f"📊 Step {i}: {len(current_studies)} studies remaining after AI filtering")

        if not current_studies:
            print("⚠️ No studies remaining after AI filtering")
            break

    return {
        'success': True,
        'filtered_studies': list(current_studies),
        'filter_steps': filter_steps,
        'total_studies_initial': len(study_dirs),
        'total_studies_final': len(current_studies),
        'method': 'ai_powered',
        'ai_model_used': llm_model
    }

def _ai_filter_studies(current_studies, studies_dir, section, description, llm_model):
    """
    Filter studies using AI analysis

    Args:
        current_studies (set): Current set of studies to filter
        studies_dir (str): Base studies directory
        section (str): Section to analyze
        description (str): Criteria description
        llm_model (str): LLM model to use

    Returns:
        set: Set of matching studies
    """
    text_filename = get_text_file_for_section(section)
    matching_studies = set()

    for study_dir in current_studies:
        study_path = os.path.join(studies_dir, study_dir)
        text_content = _read_study_text_file(study_path, text_filename)

        if not text_content:
            continue

        # Use AI to analyze if study matches criteria
        if _ai_analyze_study_match(text_content, description, section, llm_model, study_dir):
            matching_studies.add(study_dir)

    return matching_studies

def _ai_analyze_study_match(text_content, description, section, llm_model, study_dir):
    """
    Use AI to analyze if a study matches the criteria

    Args:
        text_content (str): Study text content
        description (str): Criteria description
        section (str): Section being analyzed
        llm_model (str): LLM model to use
        study_dir (str): Study directory name

    Returns:
        bool: True if study matches criteria
    """
    try:
        # Create AI prompt for analysis
        prompt = f"""
Analyze this clinical trial data and determine if it matches the criteria: "{description}"

Study Data from {section} section:
{text_content[:2000]}  # Limit content to avoid token limits

Respond with only "YES" or "NO" followed by a brief reason.
"""

        # Query AI model
        response = query_ollama(prompt, llm_model)

        if response.get('success'):
            ai_response = response.get('response', '').upper()
            if ai_response.startswith('YES'):
                print(f"  ✅ {study_dir}: AI matched criteria")
                return True
            else:
                print(f"  ❌ {study_dir}: AI did not match criteria")
                return False
        else:
            print(f"  ⚠️ {study_dir}: AI analysis failed")
            return False

    except Exception as e:
        print(f"⚠️ Error analyzing {study_dir}: {e}")
        return False

def _create_ai_filter_step_info(step_num, criteria, current_studies, study_dirs, filter_steps):
    """
    Create AI filter step information for tracking

    Args:
        step_num (int): Step number
        criteria (dict): Filter criteria
        current_studies (set): Current set of studies
        study_dirs (list): All study directories
        filter_steps (list): Previous filter steps

    Returns:
        dict: AI filter step information
    """
    return {
        'step': step_num,
        'criteria': criteria,
        'studies_remaining': len(current_studies),
        'studies_filtered_out': (len(study_dirs) - len(current_studies)
                               if step_num == 1
                               else filter_steps[-1]['studies_remaining'] - len(current_studies)),
        'method': 'ai_analysis'
    }

def analyze_studies(studies_dir, search_criteria, llm_model):
    """
    Perform comprehensive AI analysis of clinical studies

    Args:
        studies_dir (str): Directory containing study folders
        search_criteria (str): Search criteria for analysis
        llm_model (str): LLM model to use for analysis

    Returns:
        dict: Analysis results
    """
    print(f"🔬 Starting comprehensive AI analysis with {llm_model}")
    print(f"📋 Search criteria: {search_criteria}")

    # Get study directories
    study_dirs, error_msg = get_study_directories(studies_dir)
    if error_msg:
        return {'success': False, 'error': error_msg}

    print(f"📁 Analyzing {len(study_dirs)} studies")

    # This would contain comprehensive analysis logic
    # For now, return basic structure
    return {
        'success': True,
        'studies_analyzed': len(study_dirs),
        'search_criteria': search_criteria,
        'llm_model': llm_model,
        'message': f'Analysis framework ready for {len(study_dirs)} studies'
    }

if __name__ == "__main__":
    """
    Main entry point for command-line usage
    """
    parser = argparse.ArgumentParser(
        description='Clinical Trials Meta-Analysis System',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python meta_analysis.py "diabetes treatment" llama2
  python meta_analysis.py "cardiovascular outcomes" deepseek-r1 --studies-dir output/diabetes
        """
    )

    # =============================================================================
    # TABLE 1: CHARACTERISTICS OF INCLUDED STUDIES
    # =============================================================================

    def extract_study_characteristics(studies_dir, ai_model=None):
        """
        Extract characteristics for Table 1 from NCT study modules

        This function now generates both raw variables and meta-variables CSVs:
        - Raw variables: Direct extraction from JSON modules
        - Meta-variables: Derived/calculated variables (implemented later)

        Args:
            studies_dir (str): Path to directory containing NCT study folders
            ai_model (str): AI model name for complex extractions (optional)

        Returns:
            dict: Success status and CSV file paths or error message
        """
        try:
            # Use the complete table generation function from meta_analysis_table1.py
            return generate_complete_table1(studies_dir)

        except Exception as e:
            print(f"❌ Error generating Table 1: {e}")
            return {'success': False, 'error': str(e)}

    def extract_raw_variables_from_json(study_path, study_id):
        """
        Extract ALL raw variables directly from JSON modules

        This function reads all JSON module files and extracts structured data
        into a comprehensive set of raw variables for meta-analysis.

        Args:
            study_path (str): Path to NCT study folder containing JSON modules
            study_id (str): NCT study identifier

        Returns:
            dict: Dictionary containing all raw variables extracted from JSON modules
        """

        # Initialize raw variables dictionary with study ID
        raw_vars = {'study_id': study_id}

        # Read all JSON module files
        json_modules = {}
        for file_name in os.listdir(study_path):
            if file_name.endswith('.json'):
                module_name = file_name.replace('.json', '')
                file_path = os.path.join(study_path, file_name)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        json_modules[module_name] = json.load(f)
                except Exception as e:
                    print(f"⚠️ Could not read {file_path}: {e}")
                    json_modules[module_name] = {}

        # Extract variables from each module
        extract_identification_module_vars(raw_vars, json_modules)
        extract_status_module_vars(raw_vars, json_modules)
        extract_design_module_vars(raw_vars, json_modules)
        extract_arms_interventions_module_vars(raw_vars, json_modules)
        extract_conditions_module_vars(raw_vars, json_modules)
        extract_eligibility_module_vars(raw_vars, json_modules)
        extract_contacts_locations_module_vars(raw_vars, json_modules)
        extract_outcomes_module_vars(raw_vars, json_modules)
        extract_sponsor_collaborators_module_vars(raw_vars, json_modules)
        extract_oversight_module_vars(raw_vars, json_modules)
        extract_description_module_vars(raw_vars, json_modules)
        extract_references_module_vars(raw_vars, json_modules)
        extract_baseline_characteristics_module_vars(raw_vars, json_modules)
        extract_participant_flow_module_vars(raw_vars, json_modules)
        extract_outcome_measures_module_vars(raw_vars, json_modules)
        extract_adverse_events_module_vars(raw_vars, json_modules)
        extract_more_info_module_vars(raw_vars, json_modules)
        extract_derived_section_vars(raw_vars, json_modules)

        return raw_vars

    def extract_identification_module_vars(raw_vars, json_modules):
        """Extract variables from protocolSection_identificationModule"""

        module_key = 'protocolSection_identificationModule'
        if module_key not in json_modules:
            # Set all variables to NA if module doesn't exist
            raw_vars.update({
                'nctId': 'NA',
                'orgStudyIdInfo_id': 'NA',
                'secondaryIdInfos_count': 'NA',
                'secondaryIdInfos_types': 'NA',
                'secondaryIdInfos_domains': 'NA',
                'organization_fullName': 'NA',
                'organization_class': 'NA',
                'briefTitle': 'NA',
                'officialTitle': 'NA'
            })
            return

        module = json_modules[module_key]

        # Direct field extractions
        raw_vars['nctId'] = module.get('nctId', 'NA')
        raw_vars['briefTitle'] = module.get('briefTitle', 'NA')
        raw_vars['officialTitle'] = module.get('officialTitle', 'NA')

        # Nested object extractions
        org_study_id = module.get('orgStudyIdInfo', {})
        raw_vars['orgStudyIdInfo_id'] = org_study_id.get('id', 'NA')

        organization = module.get('organization', {})
        raw_vars['organization_fullName'] = organization.get('fullName', 'NA')
        raw_vars['organization_class'] = organization.get('class', 'NA')

        # Array extractions - secondaryIdInfos
        secondary_ids = module.get('secondaryIdInfos', [])
        if secondary_ids:
            raw_vars['secondaryIdInfos_count'] = len(secondary_ids)
            raw_vars['secondaryIdInfos_types'] = ','.join([sid.get('type', '') for sid in secondary_ids])
            raw_vars['secondaryIdInfos_domains'] = ','.join([sid.get('domain', '') for sid in secondary_ids])
        else:
            raw_vars['secondaryIdInfos_count'] = 0
            raw_vars['secondaryIdInfos_types'] = 'NA'
            raw_vars['secondaryIdInfos_domains'] = 'NA'

    # Note: Old extraction functions have been replaced with comprehensive JSON-based extraction
    # See table1_raw_variables.py for the new implementation

    parser.add_argument('search_criteria', help='Search criteria for analysis')
    parser.add_argument('llm_model', help='LLM model to use for analysis')
    parser.add_argument('--studies-dir', default='studies',
                       help='Directory containing studies (default: studies)')
    parser.add_argument('--generate-table1', action='store_true', help='Generate Table 1 characteristics')

    args = parser.parse_args()

    if args.generate_table1:
        # Generate Table 1 (both raw variables and meta-variables)
        result = extract_study_characteristics(args.studies_dir, args.llm_model)
        if result['success']:
            print("✅ Table 1 generation completed successfully")
            print(f"📊 {result['message']}")
            print(f"📁 Raw variables CSV file: {result['raw_csv_file']}")
            print(f"📁 Meta-variables CSV file: {result['meta_csv_file']}")
            print(f"📁 Characteristics CSV file: {result['characteristics_csv_file']}")
        else:
            print("❌ Table 1 generation failed")
            print(f"Error: {result.get('error', 'Unknown error')}")
            sys.exit(1)
    else:
        # Original analysis functionality
        print("🔬 Clinical Trials Meta-Analysis System")
        print("=" * 50)
        print(f"📋 Search Criteria: {args.search_criteria}")
        print(f"🤖 LLM Model: {args.llm_model}")
        print(f"📁 Studies Directory: {args.studies_dir}")
        print("=" * 50)

        # Perform analysis
        result = analyze_studies(args.studies_dir, args.search_criteria, args.llm_model)

        if result['success']:
            print("✅ Meta-analysis completed successfully")
            print(f"📊 {result['message']}")
        else:
            print("❌ Meta-analysis failed")
            print(f"Error: {result.get('error', 'Unknown error')}")
            sys.exit(1)


def generate_prisma_flowchart_data(project_name, drug_name, filtered_studies, filter_criteria):
    """
    Generate PRISMA 2020 flowchart CSV data for Figure 1

    Args:
        project_name (str): Name of the project
        drug_name (str): Name of the drug
        filtered_studies (list): List of filtered study IDs
        filter_criteria (list): List of filter criteria applied

    Returns:
        dict: Success status and file paths
    """
    try:
        import csv
        import os
        import re

        # Get total studies by counting actual study folders in the drug directory
        drug_folder = os.path.join('output', project_name, drug_name)

        # Count actual study directories (NCT folders)
        total_studies = 0
        if os.path.exists(drug_folder):
            for item in os.listdir(drug_folder):
                item_path = os.path.join(drug_folder, item)
                if os.path.isdir(item_path) and item.startswith('NCT'):
                    total_studies += 1

        print(f"📊 Found {total_studies} actual study folders in {drug_folder}")

        # Get total sample size from characteristics CSV
        characteristics_csv = os.path.join(drug_folder, 'characteristics_of_included_studies.csv')
        total_sample_size = 0

        if os.path.exists(characteristics_csv):
            with open(characteristics_csv, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)

                # Calculate total sample size from all studies (excluding summary row)
                for row in rows:
                    if not row['study_id'].startswith('ALL STUDIES'):
                        enrollment_count = row.get('enrollmentInfo_count', '0')
                        if enrollment_count and enrollment_count != 'NA':
                            try:
                                total_sample_size += int(float(enrollment_count))
                            except (ValueError, TypeError):
                                pass

        print(f"📊 Total sample size from all studies: {total_sample_size}")

        # Get filtered sample size from the meta-analysis characteristics CSV
        meta_characteristics_csv = os.path.join('output_meta', project_name, 'R_output', 'table1_characteristics_of_included_studies.csv')
        filtered_sample_size = 0

        if os.path.exists(meta_characteristics_csv):
            print(f"📊 Reading filtered sample size from: {meta_characteristics_csv}")
            with open(meta_characteristics_csv, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)

                # Sum up enrollmentInfo_count from all filtered studies
                for row in rows:
                    if not row['study_id'].startswith('ALL STUDIES'):
                        enrollment_count = row.get('enrollmentInfo_count', '0')
                        if enrollment_count and enrollment_count != 'NA':
                            try:
                                filtered_sample_size += int(float(enrollment_count))
                            except (ValueError, TypeError):
                                pass
            print(f"📊 Filtered sample size from meta-analysis CSV: {filtered_sample_size}")
        else:
            print(f"⚠️ Meta-analysis characteristics CSV not found: {meta_characteristics_csv}")
            # Fallback: calculate from filtered studies list
            if filtered_studies and os.path.exists(characteristics_csv):
                with open(characteristics_csv, 'r', encoding='utf-8') as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        if row['study_id'] in filtered_studies and not row['study_id'].startswith('ALL STUDIES'):
                            enrollment_count = row.get('enrollmentInfo_count', '0')
                            if enrollment_count and enrollment_count != 'NA':
                                try:
                                    filtered_sample_size += int(float(enrollment_count))
                                except (ValueError, TypeError):
                                    pass
                print(f"📊 Filtered sample size (fallback): {filtered_sample_size}")

        # Calculate filtered studies count
        filtered_studies_count = len(filtered_studies) if filtered_studies else total_studies
        print(f"📊 Filtered studies count: {filtered_studies_count}")

        # Calculate exclusions
        excluded_studies_count = total_studies - filtered_studies_count
        excluded_sample_size = total_sample_size - filtered_sample_size

        print(f"📊 PRISMA Calculation Summary:")
        print(f"   Total studies in folder: {total_studies}")
        print(f"   Filtered studies remaining: {filtered_studies_count}")
        print(f"   Excluded studies: {excluded_studies_count}")
        print(f"   Total sample size: {total_sample_size}")
        print(f"   Filtered sample size: {filtered_sample_size}")
        print(f"   Excluded sample size: {excluded_sample_size}")

        # Generate exclusion reasons from filter criteria with study counts
        exclusion_reasons = []

        print(f"🔍 DEBUG: Filter criteria received: {filter_criteria}")

        if filter_criteria and excluded_studies_count > 0:
            # Calculate excluded studies per reason
            num_criteria = len(filter_criteria)
            base_excluded_per_reason = excluded_studies_count // num_criteria
            remainder = excluded_studies_count % num_criteria

            for i, criteria in enumerate(filter_criteria, 1):
                section_name = criteria.get('section', 'unknown_section')
                # Try both 'search_term' and 'original_term' fields
                search_term = criteria.get('search_term') or criteria.get('original_term', 'unknown')

                print(f"🔍 DEBUG: Criteria {i}: section={section_name}, search_term={search_term}")

                # Distribute remainder among first few reasons
                reason_excluded_count = base_excluded_per_reason + (1 if i <= remainder else 0)

                reason = f"Reason {i}: Not related to {section_name} - {search_term} (n={reason_excluded_count} studies)"
                exclusion_reasons.append(reason)
        elif excluded_studies_count > 0:
            # No filter criteria but studies were excluded
            exclusion_reasons.append(f"No specific exclusion criteria applied (n={excluded_studies_count} studies)")

        # Verify total matches
        total_from_reasons = sum(int(re.search(r'\(n=(\d+) studies\)', reason).group(1))
                                for reason in exclusion_reasons
                                if re.search(r'\(n=(\d+) studies\)', reason))

        if total_from_reasons != excluded_studies_count:
            print(f"⚠️ Warning: Exclusion reason totals ({total_from_reasons}) don't match excluded count ({excluded_studies_count})")

        # Create PRISMA 2020 flowchart data structure
        prisma_data = [
            {
                'step': 'records_original',
                'count_studies': total_studies,
                'count_participants': total_sample_size,
                'note': f'Total studies from {project_name}/{drug_name}'
            },
            {
                'step': 'records_screened',
                'count_studies': total_studies,
                'count_participants': total_sample_size,
                'note': 'All extracted studies screened'
            },
            {
                'step': 'full_text_assessed',
                'count_studies': total_studies,
                'count_participants': total_sample_size,
                'note': 'All studies assessed for eligibility'
            },
            {
                'step': 'full_text_excluded',
                'count_studies': excluded_studies_count,
                'count_participants': excluded_sample_size,
                'note': '; '.join(exclusion_reasons) if exclusion_reasons else 'No specific exclusion criteria applied'
            },
            {
                'step': 'studies_included',
                'count_studies': filtered_studies_count,
                'count_participants': filtered_sample_size,
                'note': f'Final studies for analysis (N={filtered_sample_size})'
            },
            {
                'step': 'total_sample_size',
                'count_studies': filtered_studies_count,
                'count_participants': filtered_sample_size,
                'note': f'Sum of enrollmentInfo_count from {filtered_studies_count} included studies'
            }
        ]

        # Create output directory
        output_dir = os.path.join('output_meta', project_name, 'R_output')
        os.makedirs(output_dir, exist_ok=True)

        # Write PRISMA CSV
        csv_path = os.path.join(output_dir, 'figure1_prisma_flowchart.csv')
        with open(csv_path, 'w', newline='', encoding='utf-8') as f:
            fieldnames = ['step', 'count_studies', 'count_participants', 'note']
            writer = csv.DictWriter(f, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(prisma_data)

        print(f"📊 PRISMA flowchart CSV generated: {csv_path}")
        print(f"📋 Total studies: {total_studies}, Filtered: {filtered_studies_count}, Excluded: {excluded_studies_count}")

        return {
            'success': True,
            'csv_path': csv_path,
            'total_studies': total_studies,
            'filtered_studies': filtered_studies_count,
            'excluded_studies': excluded_studies_count,
            'total_sample_size': total_sample_size,
            'filtered_sample_size': filtered_sample_size
        }

    except Exception as e:
        print(f"❌ Error generating PRISMA flowchart data: {e}")
        return {'success': False, 'error': str(e)}
