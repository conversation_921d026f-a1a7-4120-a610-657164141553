#!/usr/bin/env python3
"""
Drug Class Detection Module with Local LLM Support
==================================================

This module uses a local LLM (Ollama) to dynamically identify drug classes
and generate comprehensive drug lists for any therapeutic category.

Features:
- Dynamic drug class detection using local LLM
- Comprehensive drug list generation for any class
- Study count validation from ClinicalTrials.gov
- Fallback to predefined lists for common classes
"""

# Standard library imports
import json
import re

# Third-party imports
import requests

# Local imports
from find_studies import search_and_filter_studies

# Constants
DEFAULT_OLLAMA_URL = "http://localhost:11434"
STEP1_TIMEOUT = 30
STEP2_TIMEOUT = 60
STEP1_MAX_TOKENS = 500
DEFAULT_TEMPERATURE = 0.1
DEFAULT_TOP_P = 0.9

class DrugClassDetector:
    """
    Drug class detection using local LLM with two-step approach

    This class provides comprehensive drug class detection and drug list generation
    using a local Ollama LLM instance.
    """

    def __init__(self, ollama_url=DEFAULT_OLLAMA_URL):
        """
        Initialize the drug class detector

        Args:
            ollama_url (str): URL of the Ollama server
        """
        self.ollama_url = ollama_url
    
    def _create_step1_prompt(self, user_input):
        """
        Create the prompt for step 1: drug class identification

        Args:
            user_input (str): User's input (drug name or class)

        Returns:
            str: Formatted prompt for step 1
        """
        return f"""
You are a pharmaceutical expert. The user entered: "{user_input}"

Your task: Identify which therapeutic drug class this belongs to.

If the input is already a drug class name (like "SGLT2", "ACE inhibitors", "statins"), return it as is.
If the input is a specific drug name (like "canagliflozin", "lisinopril", "atorvastatin"), identify its therapeutic class.

Format your response as JSON:
{{
    "drug_class": "Exact therapeutic class name",
    "is_drug_name": true/false,
    "reasoning": "Brief explanation"
}}

Examples:
- Input "canagliflozin" → {{"drug_class": "SGLT2 inhibitors", "is_drug_name": true, "reasoning": "Canagliflozin is an SGLT2 inhibitor"}}
- Input "SGLT2" → {{"drug_class": "SGLT2 inhibitors", "is_drug_name": false, "reasoning": "Already a drug class name"}}

Respond ONLY with valid JSON, no other text.
"""

    def _execute_step1(self, user_input, selected_model, temperature):
        """
        Execute step 1: identify drug class

        Args:
            user_input (str): User's input
            selected_model (str): LLM model to use
            temperature (float): Model temperature

        Returns:
            str or None: Identified drug class or None if failed
        """
        print(f"🔍 Step 1: Identifying drug class for '{user_input}'...")

        step1_prompt = self._create_step1_prompt(user_input)

        try:
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": selected_model,
                    "prompt": step1_prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "top_p": DEFAULT_TOP_P,
                        "num_predict": STEP1_MAX_TOKENS
                    }
                },
                timeout=STEP1_TIMEOUT
            )

            if response.status_code != 200:
                print(f"❌ Step 1 failed with status: {response.status_code}")
                return None

            result = response.json()
            llm_response = result.get('response', '').strip()
            print(f"📝 Step 1 Response: {llm_response}")

            return self._parse_step1_response(llm_response)

        except Exception as e:
            print(f"❌ Step 1 error: {e}")
            return None

    def _parse_step1_response(self, llm_response):
        """
        Parse the JSON response from step 1

        Args:
            llm_response (str): Raw LLM response

        Returns:
            str or None: Extracted drug class or None if parsing failed
        """
        try:
            json_start = llm_response.find('{')
            json_end = llm_response.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_str = llm_response[json_start:json_end]
                step1_result = json.loads(json_str)
                drug_class = step1_result.get('drug_class', '')

                if drug_class:
                    print(f"✅ Step 1: Identified drug class as '{drug_class}'")
                    return drug_class
                else:
                    print("❌ Step 1 failed: No drug class identified")
                    return None
            else:
                print("❌ Step 1 failed: No valid JSON found")
                return None

        except json.JSONDecodeError as e:
            print(f"❌ Step 1 JSON parse error: {e}")
            return None

    def detect_drug_class_with_llm(self, user_input, selected_model=None, temperature=DEFAULT_TEMPERATURE, max_tokens=2048):
        """
        Use local LLM with two-step approach to detect drug class and generate comprehensive drug list

        Args:
            user_input (str): User's input (drug name or class)
            selected_model (str): LLM model to use
            temperature (float): Model temperature setting
            max_tokens (int): Maximum tokens for response

        Returns:
            dict or None: Detection result or None if failed
        """
        if not selected_model:
            raise ValueError("No LLM model selected. Please configure your AI model in the AI Model tab.")

        try:
            # Step 1: Identify the drug class
            drug_class = self._execute_step1(user_input, selected_model, temperature)
            if not drug_class:
                return None

            # Step 2: Get comprehensive drug list for the identified class
            return self._execute_step2(drug_class, selected_model, temperature, max_tokens)

        except Exception as e:
            print(f"❌ Error in drug class detection: {e}")
            return None

    def _create_step2_prompt(self, drug_class):
        """
        Create the prompt for step 2: comprehensive drug list generation

        Args:
            drug_class (str): Identified drug class

        Returns:
            str: Formatted prompt for step 2
        """
        return f"""
You are a pharmaceutical expert. Generate a COMPREHENSIVE list of ALL drugs in the therapeutic class: "{drug_class}"

Your task: Provide a COMPLETE and DETAILED list of ALL drugs in the "{drug_class}" therapeutic class.

IMPORTANT: Be COMPREHENSIVE - include as many drugs as possible that belong to this therapeutic class.
Include:
- All generations of drugs in the class (older and newer)
- Both commonly prescribed and less common drugs
- Generic and brand name combinations
- International drugs if applicable
- Experimental/investigational drugs if they belong to the class
- Approved, withdrawn, and investigational drugs

Format your response as JSON:
{{
    "drug_class": "{drug_class}",
    "description": "Brief description of what this drug class treats and their mechanism of action",
    "drugs": ["generic_name (Brand_Name)", "generic_name2 (Brand_Name2)", ...],
    "reasoning": "Brief explanation of the mechanism of action"
}}

IMPORTANT:
- Always format as "generic_name (Brand_Name)"
- If no brand name exists, just use "generic_name"
- Be COMPREHENSIVE - aim for 20-50 drugs if the class is large
- Include ALL drugs in the therapeutic class
- Include investigational/experimental drugs if they belong to the class
- Focus on completeness of the ENTIRE drug class

Respond ONLY with valid JSON, no other text.
"""

    def _execute_step2(self, drug_class, selected_model, temperature, max_tokens):
        """
        Execute step 2: generate comprehensive drug list

        Args:
            drug_class (str): Identified drug class
            selected_model (str): LLM model to use
            temperature (float): Model temperature
            max_tokens (int): Maximum tokens for response

        Returns:
            dict or None: Step 2 result or None if failed
        """
        print(f"🔍 Step 2: Getting comprehensive drug list for '{drug_class}'...")

        step2_prompt = self._create_step2_prompt(drug_class)

        try:
            response = requests.post(
                f"{self.ollama_url}/api/generate",
                json={
                    "model": selected_model,
                    "prompt": step2_prompt,
                    "stream": False,
                    "options": {
                        "temperature": temperature,
                        "top_p": DEFAULT_TOP_P,
                        "num_predict": max_tokens
                    }
                },
                timeout=STEP2_TIMEOUT
            )

            if response.status_code != 200:
                print(f"❌ Step 2 failed with status: {response.status_code}")
                return None

            result = response.json()
            llm_response = result.get('response', '').strip()
            print(f"📝 Step 2 Response: {llm_response}")

            return self._parse_step2_response(llm_response, drug_class)

        except Exception as e:
            print(f"❌ Step 2 error: {e}")
            return None

    def _parse_step2_response(self, llm_response, drug_class):
        """
        Parse the JSON response from step 2

        Args:
            llm_response (str): Raw LLM response
            drug_class (str): Drug class being processed

        Returns:
            dict or None: Parsed result or None if parsing failed
        """
        try:
            # Extract JSON from response (in case there's extra text)
            json_start = llm_response.find('{')
            json_end = llm_response.rfind('}') + 1

            if json_start >= 0 and json_end > json_start:
                json_str = llm_response[json_start:json_end]
                drug_class_info = json.loads(json_str)

                # Validate required fields
                if 'drug_class' in drug_class_info and 'drugs' in drug_class_info:
                    print(f"✅ Step 2: Generated {len(drug_class_info['drugs'])} drugs for {drug_class}")
                    return drug_class_info
                else:
                    print(f"❌ Step 2 failed: Missing required fields in LLM response")
                    return None
            else:
                print(f"❌ Step 2 failed: No valid JSON found")
                return None

        except json.JSONDecodeError as e:
            print(f"❌ Step 2 JSON parse error: {e}")
            print(f"Raw response: {llm_response}")
            return None
    
    def detect_drug_class(self, user_input, selected_model=None, temperature=DEFAULT_TEMPERATURE, max_tokens=2048):
        """
        Main method to detect drug class - uses LLM only, no fallbacks

        Args:
            user_input (str): User's input (drug name or class)
            selected_model (str): LLM model to use
            temperature (float): Model temperature setting
            max_tokens (int): Maximum tokens for response

        Returns:
            dict or None: Drug class information or None if failed
        """
        if not selected_model:
            raise ValueError("No LLM model selected. Please configure your AI model in the AI Model tab.")

        print(f"🔍 Detecting drug class for: '{user_input}' using model: {selected_model}")

        # Use LLM only - no fallbacks
        llm_result = self.detect_drug_class_with_llm(user_input, selected_model, temperature, max_tokens)

        if llm_result:
            return {
                'name': llm_result.get('drug_class', 'Unknown'),
                'drugs': llm_result.get('drugs', []),
                'description': llm_result.get('description', ''),
                'reasoning': llm_result.get('reasoning', ''),
                'source': 'llm'
            }

        return None

    def _extract_generic_name(self, drug_full_name):
        """
        Extract generic drug name from full name (removes brand name in parentheses)

        Args:
            drug_full_name (str): Full drug name potentially with brand name

        Returns:
            str: Generic drug name
        """
        # Extract generic name (before parentheses)
        if '(' in drug_full_name:
            return drug_full_name.split('(')[0].strip()
        return drug_full_name.strip()
    
    def get_drugs_with_study_counts(self, drug_list, max_drugs=None):
        """
        Get study counts for drugs in real-time by querying ClinicalTrials.gov

        Args:
            drug_list (list): List of drug names to check
            max_drugs (int, optional): Maximum number of drugs to process

        Returns:
            list: List of drugs with study counts
        """
        drugs_with_studies = []
        total_drugs = len(drug_list)

        print(f"🔍 Checking study availability for {total_drugs} drugs in real-time...")

        for i, drug_full_name in enumerate(drug_list):
            try:
                # Extract generic name for searching
                generic_name = self._extract_generic_name(drug_full_name)
                print(f"  📊 Checking {drug_full_name} - searching for '{generic_name}' ({i+1}/{total_drugs})...")

                # Get study count for this drug
                study_count = self._get_study_count_for_drug(generic_name)

                # Include ALL drugs, even those with 0 studies (show as N/A)
                drugs_with_studies.append({
                    'name': drug_full_name,  # Keep full name for display
                    'study_count': study_count if study_count > 0 else 'N/A'
                })

                if study_count > 0:
                    print(f"    ✅ {drug_full_name}: {study_count} studies")
                else:
                    print(f"    ❌ {drug_full_name}: N/A (no studies found)")

            except Exception as e:
                print(f"    ⚠️ {drug_full_name}: Error checking studies - {e}")
                # Still include the drug but mark as N/A
                drugs_with_studies.append({
                    'name': drug_full_name,
                    'study_count': 'N/A'
                })
                continue

        return self._sort_drugs_by_study_count(drugs_with_studies)

    def _get_study_count_for_drug(self, drug_name):
        """
        Get study count for a specific drug

        Args:
            drug_name (str): Drug name to search for

        Returns:
            int: Number of studies found
        """
        try:
            studies = search_and_filter_studies(drug_name)
            return len(studies) if studies else 0
        except Exception:
            return 0

    def _sort_drugs_by_study_count(self, drugs_with_studies):
        """
        Sort drugs by study count (N/A goes to end)

        Args:
            drugs_with_studies (list): List of drugs with study counts

        Returns:
            list: Sorted list of drugs
        """
        def sort_key(x):
            if x['study_count'] == 'N/A':
                return -1
            return x['study_count']

        return sorted(drugs_with_studies, key=sort_key, reverse=True)

    def filter_negative_classifications(self, drug_list):
        """
        Filter out drugs that have negative classifications in their names

        Args:
            drug_list (list): List of drug names to filter

        Returns:
            list: Filtered list without negative classifications
        """
        negative_patterns = [
            'not a statin', 'not a ', 'not an ', 'note: this is a', 'note: this is an',
            '- note:', 'this is not', 'not actually', 'different class',
            'different mechanism', 'not in this class', 'belongs to a different',
            'actually a ', 'technically a ', 'classified as a different'
        ]

        filtered_drugs = []
        for drug in drug_list:
            drug_lower = drug.lower()
            is_negative = any(pattern in drug_lower for pattern in negative_patterns)

            if is_negative:
                print(f"  ❌ Filtering out: {drug} (contains negative classification)")
            else:
                filtered_drugs.append(drug)

        return filtered_drugs

    def deduplicate_drugs(self, drug_list):
        """
        Remove duplicate drugs based on generic name (before parentheses)

        Args:
            drug_list (list): List of drug names to deduplicate

        Returns:
            list: Deduplicated list of drugs
        """
        seen_generics = set()
        deduplicated_drugs = []

        for drug in drug_list:
            # Extract generic name (everything before the first parenthesis or space)
            generic_name = drug.split('(')[0].strip().lower()
            # Handle cases like "dapagliflozin injection" -> "dapagliflozin"
            generic_name = generic_name.split()[0].strip().lower()

            if generic_name not in seen_generics:
                seen_generics.add(generic_name)
                deduplicated_drugs.append(drug)
                print(f"  ✅ Keeping: {drug} (generic: {generic_name})")
            else:
                print(f"  🔄 Duplicate removed: {drug} (generic: {generic_name} already exists)")

        return deduplicated_drugs

    def process_drug_class_request(self, user_input, selected_model=None, temperature=DEFAULT_TEMPERATURE, max_tokens=2048):
        """
        Main method to process a drug class request with real-time LLM analysis

        Args:
            user_input (str): User's input (drug name or class)
            selected_model (str): LLM model to use
            temperature (float): Model temperature setting
            max_tokens (int): Maximum tokens for response

        Returns:
            dict: Processing result with success status and data
        """
        if not selected_model:
            return {
                'success': False,
                'message': "No LLM model selected. Please configure your AI model in the AI Model tab first."
            }

        print(f"🔍 Processing drug class request: '{user_input}' with LLM model: {selected_model}")

        # Detect drug class using LLM
        try:
            class_info = self.detect_drug_class(user_input, selected_model, temperature, max_tokens)
        except Exception as e:
            return {
                'success': False,
                'message': f"LLM processing failed: {str(e)}. Please check your AI model configuration."
            }

        if not class_info:
            return {
                'success': False,
                'message': f"LLM could not identify drug class from: '{user_input}'. Please try a different term or check your AI model configuration."
            }

        print(f"✅ LLM identified drug class: {class_info['name']}")
        print(f"📝 Description: {class_info['description']}")

        return self._process_drug_list(class_info)

    def _process_drug_list(self, class_info):
        """
        Process the drug list from LLM: filter, deduplicate, and get study counts

        Args:
            class_info (dict): Drug class information from LLM

        Returns:
            dict: Processed result with filtered drugs and study counts
        """
        print(f"💊 LLM generated {len(class_info['drugs'])} drugs in class")
        print(f"🧠 Reasoning: {class_info.get('reasoning', 'N/A')}")

        # Filter out drugs with negative classifications (e.g., "not a statin")
        filtered_drugs = self.filter_negative_classifications(class_info['drugs'])
        print(f"🔍 Filtered {len(class_info['drugs']) - len(filtered_drugs)} drugs with negative classifications")

        # Remove duplicate drugs based on generic name
        deduplicated_drugs = self.deduplicate_drugs(filtered_drugs)
        print(f"🔍 Removed {len(filtered_drugs) - len(deduplicated_drugs)} duplicate drugs")

        # Get study counts for deduplicated drugs in real-time
        drugs_with_studies = self.get_drugs_with_study_counts(deduplicated_drugs)

        # Count drugs with actual studies (not N/A)
        drugs_with_actual_studies = [d for d in drugs_with_studies if d['study_count'] != 'N/A']

        return {
            'success': True,
            'drug_class': class_info['name'],
            'description': class_info['description'],
            'reasoning': class_info.get('reasoning', ''),
            'source': 'llm',
            'drugs_with_studies': drugs_with_studies,  # All drugs (including N/A)
            'drugs_with_actual_studies': len(drugs_with_actual_studies),
            'message': f"LLM found {len(deduplicated_drugs)} drugs in {class_info['name']} (filtered from {len(class_info['drugs'])} initial results, removed {len(filtered_drugs) - len(deduplicated_drugs)} duplicates). {len(drugs_with_actual_studies)} have clinical trial data available."
        }

def main():
    """Test the drug class detector"""
    detector = DrugClassDetector()
    
    # Test cases
    test_inputs = [
        "anticonvulsants",
        "seizure medications", 
        "epilepsy drugs",
        "sglt2 inhibitors",
        "antidepressants",
        "beta blockers"
    ]
    
    for test_input in test_inputs:
        print(f"\n{'='*60}")
        result = detector.process_drug_class_request(test_input)
        print(f"Result: {json.dumps(result, indent=2)}")

if __name__ == "__main__":
    main()
