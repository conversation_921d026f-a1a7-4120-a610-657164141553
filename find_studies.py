#!/usr/bin/env python3
"""
Clinical Trials Study Search and Filtering
==========================================

This module provides search and filtering functionality for clinical trials
without performing data extraction. Used for testing and validation purposes.

Key Functions:
- search_and_filter_studies(): Search for studies by drug name
- filter_studies_by_criteria(): Apply filtering criteria to study results

Author: Clinical Trials Analysis System
Version: 2.0 - Clean Architecture
"""

# Standard library imports
import json
import os

# Third-party imports
import requests

# Constants
API_BASE_URL = "https://clinicaltrials.gov/api/v2/studies"
DEFAULT_PAGE_SIZE = 1000
REQUEST_TIMEOUT = 60
DEFAULT_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'

def search_and_filter_studies(drug_name="Canagliflozin"):
    """
    Search and filter studies to see how many match our criteria

    Args:
        drug_name (str): Name of the drug to search for

    Returns:
        dict: Search results with study information
    """
    print(f"🔍 Searching for {drug_name} studies...")

    # Build API URL
    api_url = f"{API_BASE_URL}?query.term={drug_name}&pageSize={DEFAULT_PAGE_SIZE}&format=json"

    headers = {
        'User-Agent': DEFAULT_USER_AGENT,
        'Accept': 'application/json',
    }

    try:
        response = requests.get(api_url, headers=headers, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            data = response.json()
            studies = data.get('studies', [])
            print(f"✅ Found {len(studies)} total studies")

            # Filter studies by criteria
            filtered_results = filter_studies_by_criteria(studies)

            # Display results
            display_filtering_results(filtered_results)

            # Return completed studies with results for further processing
            return filtered_results['completed_with_results']

        else:
            print(f"❌ API request failed with status {response.status_code}")
            return []

    except requests.exceptions.Timeout:
        print(f"⏰ Request timeout after {REQUEST_TIMEOUT} seconds")
        return []
    except requests.exceptions.RequestException as e:
        print(f"❌ Request error: {e}")
        return []
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return []

def filter_studies_by_criteria(studies):
    """
    Filter studies based on completion status and results availability

    Args:
        studies (list): List of study data from API

    Returns:
        dict: Categorized studies
    """
    completed_with_results = []
    completed_without_results = []
    not_completed = []

    for study in studies:
        try:
            study_info = extract_study_info(study)

            if study_info['is_completed'] and study_info['has_results']:
                completed_with_results.append(study_info)
            elif study_info['is_completed'] and not study_info['has_results']:
                completed_without_results.append(study_info)
            else:
                not_completed.append(study_info)

        except Exception as e:
            print(f"⚠️ Error processing study: {e}")
            continue

    return {
        'total_studies': len(studies),
        'completed_with_results': completed_with_results,
        'completed_without_results': completed_without_results,
        'not_completed': not_completed
    }

def extract_study_info(study):
    """
    Extract relevant information from a study

    Args:
        study (dict): Study data from API

    Returns:
        dict: Extracted study information
    """
    protocol_section = study.get('protocolSection', {})
    results_section = study.get('resultsSection', {})

    identification_module = protocol_section.get('identificationModule', {})
    status_module = protocol_section.get('statusModule', {})

    return {
        'nct_id': identification_module.get('nctId', ''),
        'title': identification_module.get('briefTitle', ''),
        'status': status_module.get('overallStatus', ''),
        'is_completed': status_module.get('overallStatus', '') == 'COMPLETED',
        'has_results': bool(results_section)
    }

def display_filtering_results(filtered_results):
    """
    Display filtering results in a formatted way

    Args:
        filtered_results (dict): Results from filter_studies_by_criteria
    """
    print(f"\n📊 FILTERING RESULTS:")
    print(f"Total studies: {filtered_results['total_studies']}")
    print(f"Completed with results: {len(filtered_results['completed_with_results'])}")
    print(f"Completed without results: {len(filtered_results['completed_without_results'])}")
    print(f"Not completed: {len(filtered_results['not_completed'])}")

    print(f"\n✅ STUDIES MATCHING CRITERIA ({len(filtered_results['completed_with_results'])}):")

    # Display matching studies
    for i, study in enumerate(filtered_results['completed_with_results'], 1):
        print(f"{i:2d}. {study['nct_id']}: {study['title'][:60]}...")

def save_studies_to_file(studies, drug_name, output_dir="output"):
    """
    Save matching studies to a JSON file

    Args:
        studies (list): List of matching studies
        drug_name (str): Name of the drug
        output_dir (str): Output directory

    Returns:
        str: Path to saved file
    """
    try:
        os.makedirs(output_dir, exist_ok=True)
        output_filename = os.path.join(output_dir, f"{drug_name.lower()}_matching_studies.json")

        with open(output_filename, 'w', encoding='utf-8') as f:
            json.dump(studies, f, indent=2, ensure_ascii=False)

        print(f"💾 Saved {len(studies)} matching studies to {output_filename}")
        return output_filename

    except Exception as e:
        print(f"❌ Error saving studies: {e}")
        return None

def get_study_details(nct_id):
    """
    Get detailed information for a specific study

    Args:
        nct_id (str): NCT ID of the study

    Returns:
        dict or None: Study details or None if failed
    """
    api_url = f"{API_BASE_URL}/{nct_id}"
    headers = {
        'User-Agent': DEFAULT_USER_AGENT,
        'Accept': 'application/json'
    }

    try:
        response = requests.get(api_url, headers=headers, timeout=REQUEST_TIMEOUT)

        if response.status_code == 200:
            return response.json()
        else:
            print(f"❌ Error fetching {nct_id}: {response.status_code}")
            return None

    except Exception as e:
        print(f"❌ Error fetching {nct_id}: {e}")
        return None

def validate_study_data(study_data):
    """
    Validate that study data contains required fields

    Args:
        study_data (dict): Study data to validate

    Returns:
        bool: True if valid, False otherwise
    """
    try:
        protocol_section = study_data.get('protocolSection', {})
        identification_module = protocol_section.get('identificationModule', {})

        # Check for required fields
        required_fields = ['nctId', 'briefTitle']
        for field in required_fields:
            if not identification_module.get(field):
                return False

        return True

    except Exception:
        return False

def main():
    """
    Main function for command-line usage
    """
    import argparse

    parser = argparse.ArgumentParser(
        description='Clinical Trials Study Search and Filtering',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python find_studies.py canagliflozin
  python find_studies.py "diabetes medication" --output-dir custom_output
        """
    )

    parser.add_argument('drug_name', nargs='?', default='Canagliflozin',
                       help='Name of the drug to search for (default: Canagliflozin)')
    parser.add_argument('--output-dir', default='output',
                       help='Output directory for results (default: output)')
    parser.add_argument('--save-results', action='store_true',
                       help='Save results to JSON file')

    args = parser.parse_args()

    print("🔍 Clinical Trials Study Search System")
    print("=" * 50)
    print(f"💊 Drug Name: {args.drug_name}")
    print(f"📁 Output Directory: {args.output_dir}")
    print("=" * 50)

    # Search and filter studies
    matching_studies = search_and_filter_studies(args.drug_name)

    if matching_studies:
        print(f"\n🎯 Found {len(matching_studies)} studies that match all criteria!")

        # Save results if requested
        if args.save_results:
            save_studies_to_file(matching_studies, args.drug_name, args.output_dir)
    else:
        print(f"\n❌ No matching studies found for {args.drug_name}")

if __name__ == "__main__":
    main()
