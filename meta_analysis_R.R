#!/usr/bin/env Rscript
# =============================================================================
# Clinical Trials Meta-Analysis R Script
# =============================================================================
# 
# This script contains all R code for meta-analysis of clinical trials data.
# All R outputs are saved to output_meta/project_name/R_output/
#
# Author: Clinical Trials Meta-Analysis Platform
# =============================================================================

# Load required libraries
library(DiagrammeR)
library(glue)
library(DiagrammeRsvg)
library(rsvg)

# =============================================================================
# PRISMA 2020 Flow Diagram Generation
# =============================================================================

generate_prisma_diagram <- function(project_name, drug_name, 
                                   records_original = 0,
                                   records_screened = 0,
                                   full_text_assessed = 0,
                                   full_text_excluded = 0,
                                   exclusion_reasons = c(),
                                   studies_included = 0,
                                   total_sample_size = 0) {
  
  # Create output directory
  output_dir <- file.path("output_meta", project_name, "R_output")
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  # Format exclusion reasons
  if (length(exclusion_reasons) > 0) {
    exclusion_text <- paste(exclusion_reasons, collapse = "\n")
  } else {
    exclusion_text <- "No specific reasons provided"
  }
  
  # Create excluded text with glue
  excluded <- glue('Full text articles excluded
               n = {full_text_excluded}
               Reasons for exclusion
               {exclusion_text}')
  
# Create PRISMA diagram
prisma_diagram <- grViz("
  digraph cohort_flow_chart
  {
    node [fontname = Helvetica, fontsize = 12, shape = box, width = 4]
    i[label = 'Identification', fillcolor = LightBlue, style = filled, width = 2]
    j[label = 'Screening',fillcolor = LightBlue, style = filled, width = 2]
    k[label = 'Eligibility', fillcolor = LightBlue, style = filled, width = 2]
    l[label = 'Included', fillcolor = LightBlue, style = filled, width = 2]

    a[label = 'Records identified from clinicaltrials.gov\\nn = {records_original}']
    d[label = 'Records screened\\nn = {records_screened}']
    f[label = 'Full text articles assessed\\nn = {full_text_assessed}']
    g[label = 'Studies included\\nn = {studies_included}\\nN = {total_sample_size}']
    h[label = '@@1']
    blank_1[label = '', width = 0.01, height = 0.01]
    blank_2[label = '', width = 0.01, height = 0.01]
    blank_4[label = '', width = 4, color = White]

    { rank = same; a i}
    { rank = same; j d}
    { rank = same; f k}
    { rank = same; g l}
    { rank = same; blank_2 h}

    a -> d;
    d -> blank_1 [ dir = none ];
    blank_1 -> f;
    f -> blank_2 [ dir = none ];
    blank_2 -> h [ minlen = 3 ];
    blank_2 -> g;
  }
  [1]: excluded
  ")
  
  # Export diagram
  output_file <- file.path(output_dir, "figure1_prisma_flowchart.png")
  
  prisma_diagram %>%
    export_svg() %>%
    charToRaw() %>%
    rsvg_png(output_file)
  
  cat("✅ PRISMA flow diagram saved to:", output_file, "\n")
  
  return(output_file)
}

# =============================================================================
# Main execution function
# =============================================================================

main <- function() {
  # Get command line arguments
  args <- commandArgs(trailingOnly = TRUE)
  
  if (length(args) < 2) {
    cat("Usage: Rscript meta_analysis_R.R <project_name> <drug_name> [additional_args]\n")
    quit(status = 1)
  }
  
  project_name <- args[1]
  drug_name <- args[2]
  
  cat("🔬 Starting meta-analysis for:", project_name, "/", drug_name, "\n")

  # Read PRISMA flowchart data from CSV
  csv_path <- file.path("output_meta", project_name, "R_output", "figure1_prisma_flowchart.csv")

  if (file.exists(csv_path)) {
    cat("📊 Reading PRISMA data from:", csv_path, "\n")
    prisma_data <- read.csv(csv_path, stringsAsFactors = FALSE)

    # Extract data from CSV
    identification_row <- prisma_data[prisma_data$step == "identification", ]
    screening_row <- prisma_data[prisma_data$step == "screening", ]
    eligibility_row <- prisma_data[prisma_data$step == "eligibility", ]
    included_row <- prisma_data[prisma_data$step == "included", ]

    # Generate PRISMA diagram with actual data
    generate_prisma_diagram(
      project_name = project_name,
      drug_name = drug_name,
      records_original = identification_row$count_studies,
      records_update = 0,
      records_after_duplicates = screening_row$count_studies,
      records_screened = screening_row$count_studies,
      records_excluded = eligibility_row$count_studies,
      full_text_assessed = screening_row$count_studies,
      full_text_excluded = eligibility_row$count_studies,
      exclusion_reasons = strsplit(eligibility_row$note, "; ")[[1]],
      studies_included = included_row$count_studies,
      total_sample_size = included_row$count_participants
    )
  } else {
    cat("⚠️ PRISMA CSV not found, using placeholder values\n")
    # Fallback to placeholder values
    generate_prisma_diagram(
      project_name = project_name,
      drug_name = drug_name,
      records_original = 100,
      records_update = 0,
      records_after_duplicates = 100,
      records_screened = 100,
      records_excluded = 50,
      full_text_assessed = 100,
      full_text_excluded = 50,
      exclusion_reasons = c("No PRISMA data available"),
      studies_included = 50,
      total_sample_size = 5000
    )
  }
  
  cat("🎉 Meta-analysis R processing complete!\n")
}

# Run main function if script is executed directly
if (!interactive()) {
  main()
}
