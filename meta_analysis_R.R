#!/usr/bin/env Rscript
# =============================================================================
# Clinical Trials Meta-Analysis R Script
# =============================================================================
# 
# This script contains all R code for meta-analysis of clinical trials data.
# All R outputs are saved to output_meta/project_name/R_output/
#
# Author: Clinical Trials Meta-Analysis Platform
# =============================================================================

# Load required libraries
library(DiagrammeR)
library(glue)
library(DiagrammeRsvg)
library(rsvg)

# =============================================================================
# PRISMA 2020 Flow Diagram Generation
# =============================================================================

generate_prisma_diagram <- function(project_name, drug_name, 
                                   records_original = 0,
                                   records_update = 0, 
                                   records_after_duplicates = 0,
                                   records_screened = 0,
                                   records_excluded = 0,
                                   full_text_assessed = 0,
                                   full_text_excluded = 0,
                                   exclusion_reasons = c(),
                                   studies_included = 0,
                                   total_sample_size = 0) {
  
  # Create output directory
  output_dir <- file.path("output_meta", project_name, "R_output")
  dir.create(output_dir, recursive = TRUE, showWarnings = FALSE)
  
  # Format exclusion reasons
  if (length(exclusion_reasons) > 0) {
    exclusion_text <- paste(exclusion_reasons, collapse = "\n")
  } else {
    exclusion_text <- "No specific reasons provided"
  }
  
  # Create excluded text with glue
  excluded <- glue('Full text articles excluded
               n = {full_text_excluded}
               Reasons for exclusion
               {exclusion_text}')
  
  # Create PRISMA diagram
  prisma_diagram <- grViz("
  digraph cohort_flow_chart
  {
    node [fontname = Helvetica, fontsize = 12, shape = box, width = 4]
    i[label = 'Identification', fillcolor = LightBlue, style = filled, width = 2]
    j[label = 'Screening',fillcolor = LightBlue, style = filled, width = 2]
    k[label = 'Eligibility', fillcolor = LightBlue, style = filled, width = 2]
    l[label = 'Included', fillcolor = LightBlue, style = filled, width = 2]

    a[label = 'Records identified in original search\\nn = {records_original}']
    b[label = 'Records identified with update\\nn = {records_update}']
    c[label = 'Records after duplicates removed\\nn = {records_after_duplicates}']
    d[label = 'Records screened\\nn = {records_screened}']
    e[label = 'Records excluded\\nn = {records_excluded}']
    f[label = 'Full text articles assessed\\nn = {full_text_assessed}']
    g[label = 'Studies included\\nn = {studies_included}\\nN = {total_sample_size}']
    h[label = '@@1']
    blank_1[label = '', width = 0.01, height = 0.01]
    blank_2[label = '', width = 0.01, height = 0.01]
    blank_4[label = '', width = 4, color = White]

    { rank = same; a b i}
    { rank = same; blank_4 c j}
    { rank = same; f k}
    { rank = same; g l}
    { rank = same; blank_1 e}
    { rank = same; blank_2 h}

    a -> c;
    b -> c;
    b -> blank_4 [ dir = none, color = White];
    c -> d;
    d -> blank_1 [ dir = none ];
    blank_1 -> e [ minlen = 3 ];
    blank_1 -> f;
    f -> blank_2 [ dir = none ];
    blank_2 -> h [ minlen = 3 ];
    blank_2 -> g;
  }
  [1]: excluded
  ")
  
  # Export diagram
  output_file <- file.path(output_dir, "prisma_flow_diagram.png")
  
  prisma_diagram %>%
    export_svg() %>%
    charToRaw() %>%
    rsvg_png(output_file)
  
  cat("✅ PRISMA flow diagram saved to:", output_file, "\n")
  
  return(output_file)
}

# =============================================================================
# Main execution function
# =============================================================================

main <- function() {
  # Get command line arguments
  args <- commandArgs(trailingOnly = TRUE)
  
  if (length(args) < 2) {
    cat("Usage: Rscript meta_analysis_R.R <project_name> <drug_name> [additional_args]\n")
    quit(status = 1)
  }
  
  project_name <- args[1]
  drug_name <- args[2]
  
  cat("🔬 Starting meta-analysis for:", project_name, "/", drug_name, "\n")
  
  # For now, generate PRISMA diagram with placeholder values
  # These will be calculated from actual study data in future implementation
  generate_prisma_diagram(
    project_name = project_name,
    drug_name = drug_name,
    records_original = 1000,
    records_update = 0,
    records_after_duplicates = 950,
    records_screened = 950,
    records_excluded = 900,
    full_text_assessed = 50,
    full_text_excluded = 25,
    exclusion_reasons = c("Reason 1", "Reason 2"),
    studies_included = 25,
    total_sample_size = 5000
  )
  
  cat("🎉 Meta-analysis R processing complete!\n")
}

# Run main function if script is executed directly
if (!interactive()) {
  main()
}
