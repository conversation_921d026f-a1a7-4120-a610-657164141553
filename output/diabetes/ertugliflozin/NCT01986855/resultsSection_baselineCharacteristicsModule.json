{"populationDescription": "The Overall Number of Baseline Participants was the All-Participants-As-Treated population.", "groups": [{"id": "BG000", "title": "Ertugliflozin 5 mg", "description": "Ertugliflozin, oral, 5-mg tablet once daily for 52 weeks. Participants also received a 10-mg matching placebo tablet once daily for 52 weeks."}, {"id": "BG001", "title": "Ertugliflozin 15 mg", "description": "Ertugliflozin, oral, 5-mg and 10-mg tablet once daily for 52 weeks"}, {"id": "BG002", "title": "Placebo", "description": "Placebo, oral, tablet, 5-mg or 5-mg and 10-mg tablet once daily for 52 weeks"}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "Years", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "66.7", "spread": "8.3"}, {"groupId": "BG001", "value": "67.5", "spread": "8.5"}, {"groupId": "BG002", "value": "67.5", "spread": "8.9"}, {"groupId": "BG003", "value": "67.3", "spread": "8.6"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "74"}, {"groupId": "BG001", "value": "80"}, {"groupId": "BG002", "value": "82"}, {"groupId": "BG003", "value": "236"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "84"}, {"groupId": "BG001", "value": "75"}, {"groupId": "BG002", "value": "72"}, {"groupId": "BG003", "value": "231"}]}]}]}, {"title": "Baseline Hemoglobin A1C (A1C)", "populationDescription": "Participants with baseline data for A1C.", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "Percentage", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "154"}, {"groupId": "BG001", "value": "151"}, {"groupId": "BG002", "value": "152"}, {"groupId": "BG003", "value": "457"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "8.20", "spread": "1.02"}, {"groupId": "BG001", "value": "8.17", "spread": "0.87"}, {"groupId": "BG002", "value": "8.08", "spread": "0.89"}, {"groupId": "BG003", "value": "8.15", "spread": "0.93"}]}]}]}, {"title": "Baseline Weight", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "Kilograms", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "89.4", "spread": "22.5"}, {"groupId": "BG001", "value": "85.8", "spread": "17.4"}, {"groupId": "BG002", "value": "90.4", "spread": "18.9"}, {"groupId": "BG003", "value": "88.5", "spread": "19.8"}]}]}]}, {"title": "Baseline Fasting Plasma Glucose (FPG)", "populationDescription": "Participants with baseline data for FPG.", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "mg/dL", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "157"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "466"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "160.9", "spread": "56.4"}, {"groupId": "BG001", "value": "157.5", "spread": "47.8"}, {"groupId": "BG002", "value": "156.9", "spread": "56.4"}, {"groupId": "BG003", "value": "158.5", "spread": "53.6"}]}]}]}, {"title": "Baseline Estimated glomerular filtration rate (eGFR)", "description": "Stratification Factor: eGFR (mL/min/1.73m\\^2)", "paramType": "NUMBER", "unitOfMeasure": "Participants", "classes": [{"title": "≥30 to <45", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "52"}, {"groupId": "BG001", "value": "53"}, {"groupId": "BG002", "value": "54"}, {"groupId": "BG003", "value": "159"}]}]}, {"title": "≥45 to <60", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "106"}, {"groupId": "BG001", "value": "102"}, {"groupId": "BG002", "value": "100"}, {"groupId": "BG003", "value": "308"}]}]}]}, {"title": "Stratification Factor: Insulin at randomization", "description": "Insulin at randomization? (Yes/No)", "paramType": "NUMBER", "unitOfMeasure": "Participants", "classes": [{"title": "No", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "69"}, {"groupId": "BG001", "value": "68"}, {"groupId": "BG002", "value": "66"}, {"groupId": "BG003", "value": "203"}]}]}, {"title": "Yes", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "158"}, {"groupId": "BG001", "value": "155"}, {"groupId": "BG002", "value": "154"}, {"groupId": "BG003", "value": "467"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "89"}, {"groupId": "BG001", "value": "87"}, {"groupId": "BG002", "value": "88"}, {"groupId": "BG003", "value": "264"}]}]}]}]}