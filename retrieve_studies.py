#!/usr/bin/env python3
"""
Clinical Trials Studies Retrieval and Text Extraction
====================================================

Consolidated script that handles the complete workflow:
1. Find studies for a drug using find_studies.py logic
2. Extract individual modules from each study
3. Save each module as separate text files with section_module naming
4. Create empty files for missing modules to ensure consistency

File naming convention: {section}_{module}.txt
Examples: protocolSection_identificationModule.txt, resultsSection_participantFlowModule.txt

Author: Clinical Trials Analysis System
Version: 3.0 - Module-Based Architecture
"""

# Standard library imports
import csv
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Third-party imports
import requests

# Constants
API_BASE_URL = "https://clinicaltrials.gov/api/v2/studies"
REQUEST_TIMEOUT = 60
MAX_PAGE_SIZE = 1000
RATE_LIMIT_DELAY = 1.0  # seconds between requests
DEFAULT_USER_AGENT = 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'

class ClinicalTrialsModuleExtractor:
    """
    Clinical trials data extractor that handles the complete workflow:
    1. Find studies for a drug using ClinicalTrials.gov API
    2. Extract individual modules from each study
    3. Save each module as separate text files with section_module naming
    4. Create empty files for missing modules to ensure consistency
    """

    def __init__(self):
        """Initialize the module extractor with predefined module lists"""
        self.progress_callback = None
        self.protocol_modules = self._get_protocol_modules()
        self.results_modules = self._get_results_modules()
        self.derived_modules = self._get_derived_modules()

    def _get_protocol_modules(self):
        """Get list of protocol section modules"""
        return [
            'identificationModule',
            'statusModule',
            'oversightModule',
            'descriptionModule',
            'conditionsModule',
            'designModule',
            'armsInterventionsModule',
            'outcomesModule',
            'eligibilityModule',
            'contactsLocationsModule',
            'referencesModule',
            'ipdSharingStatementModule',
            'sponsorCollaboratorsModule',
            'largeDocumentModule'
        ]

    def _get_results_modules(self):
        """Get list of results section modules"""
        return [
            'participantFlowModule',
            'baselineCharacteristicsModule',
            'outcomeMeasuresModule',
            'adverseEventsModule',
            'moreInfoModule',
            'limitationsAndCaveatsModule',
            'certainAgreementsModule',
            'pointOfContactModule'
        ]

    def _get_derived_modules(self):
        """Get list of derived section modules"""
        return [
            'derivedSection',
            'documentSection',
            'miscInfoModule'
        ]

        self.derived_modules = self._get_derived_modules()

    def set_progress_callback(self, callback):
        """
        Set a callback function for progress updates

        Args:
            callback (callable): Function to call for progress updates
        """
        self.progress_callback = callback

    def report_progress(self, message, progress=None, total=None, nct_id=None, current=None):
        """
        Report progress to the callback if set

        Args:
            message (str): Progress message
            progress (int, optional): Current progress
            total (int, optional): Total items
            nct_id (str, optional): NCT ID being processed
            current (int, optional): Current item number
        """
        if self.progress_callback:
            self.progress_callback(message, progress, total, nct_id, current)
        else:
            if nct_id and current and total:
                print(f"{message} - {nct_id} ({current}/{total})")
            elif progress is not None and total is not None:
                print(f"{message} - {progress}/{total}")
            else:
                print(message)

    def get_study_from_api(self, nct_id):
        """
        Get study data from ClinicalTrials.gov API v2

        Args:
            nct_id (str): NCT ID of the study to fetch

        Returns:
            dict or None: Study data or None if failed
        """
        url = f"{API_BASE_URL}/{nct_id}"

        headers = {
            'User-Agent': DEFAULT_USER_AGENT,
            'Accept': 'application/json'
        }

        try:
            response = requests.get(url, headers=headers, timeout=REQUEST_TIMEOUT)

            if response.status_code == 200:
                return response.json()
            else:
                print(f"❌ Error fetching {nct_id}: {response.status_code} - {response.text}")
                return None

        except requests.exceptions.Timeout:
            print(f"⏰ Timeout fetching {nct_id}")
            return None
        except Exception as e:
            print(f"❌ Exception fetching {nct_id}: {e}")
            return None

    def extract_module_data(self, study_data, section, module):
        """
        Extract JSON data from a specific module

        Args:
            study_data (dict): Study data from API
            section (str): Section name (e.g., 'protocolSection')
            module (str): Module name (e.g., 'identificationModule')

        Returns:
            dict: Module data or empty dict if not found
        """
        try:
            if section in study_data and module in study_data[section]:
                return study_data[section][module]
            return {}
        except Exception as e:
            print(f"❌ Error extracting {section}.{module}: {e}")
            return {}

    def save_module_json(self, output_dir, section, module, json_data):
        """
        Save module JSON data to .json file

        Args:
            output_dir (str): Output directory path
            section (str): Section name
            module (str): Module name
            json_data (dict): Module JSON data

        Returns:
            str: File path where JSON was saved
        """
        filename = f"{section}_{module}.json"
        filepath = os.path.join(output_dir, filename)

        try:
            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(json_data, f, indent=2, ensure_ascii=False)
            return filepath
        except Exception as e:
            print(f"❌ Error saving {filepath}: {e}")
            return None

    def save_module_text(self, output_dir, section, module, json_data):
        """
        Convert JSON data to structured text and save to .txt file

        Args:
            output_dir (str): Output directory path
            section (str): Section name
            module (str): Module name
            json_data (dict): Module JSON data

        Returns:
            str: File path where text was saved
        """
        filename = f"{section}_{module}.txt"
        filepath = os.path.join(output_dir, filename)

        try:
            # Convert JSON to structured text
            structured_text = self.json_to_structured_text(json_data, section, module)

            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(structured_text)
            return filepath
        except Exception as e:
            print(f"❌ Error saving {filepath}: {e}")
            return None

    def json_to_structured_text(self, json_data, section, module):
        """
        Convert JSON data to structured, readable text format for AI processing

        Args:
            json_data (dict): JSON data to convert
            section (str): Section name
            module (str): Module name

        Returns:
            str: Structured text representation
        """
        if not json_data:
            return f"# {section.upper()} - {module.upper()}\n\nNo data available.\n"

        lines = []
        lines.append(f"# {section.upper()} - {module.upper()}")
        lines.append("=" * 60)
        lines.append("")

        # Convert JSON to readable format
        self._json_to_text_recursive(json_data, lines, 0)

        return "\n".join(lines)

    def _json_to_text_recursive(self, data, lines, indent_level):
        """
        Recursively convert JSON data to structured text

        Args:
            data: JSON data (dict, list, or primitive)
            lines (list): List to append text lines to
            indent_level (int): Current indentation level
        """
        indent = "  " * indent_level

        if isinstance(data, dict):
            for key, value in data.items():
                if isinstance(value, (dict, list)) and value:
                    lines.append(f"{indent}{key.upper()}:")
                    self._json_to_text_recursive(value, lines, indent_level + 1)
                    lines.append("")
                elif value is not None and value != "":
                    # Format the value nicely
                    formatted_value = self._format_value(value)
                    lines.append(f"{indent}{key}: {formatted_value}")

        elif isinstance(data, list):
            for i, item in enumerate(data):
                if isinstance(item, dict):
                    lines.append(f"{indent}[{i+1}]")
                    self._json_to_text_recursive(item, lines, indent_level + 1)
                    lines.append("")
                else:
                    formatted_value = self._format_value(item)
                    lines.append(f"{indent}- {formatted_value}")

        else:
            formatted_value = self._format_value(data)
            lines.append(f"{indent}{formatted_value}")

    def _format_value(self, value):
        """
        Format a value for text output

        Args:
            value: Value to format

        Returns:
            str: Formatted value
        """
        if isinstance(value, str):
            # Clean up long strings and add line breaks for readability
            if len(value) > 100:
                return value.replace('. ', '.\n    ')
            return value
        elif isinstance(value, bool):
            return "Yes" if value else "No"
        elif value is None:
            return "Not specified"
        else:
            return str(value)

    def process_adverse_events_to_csv(self, study_dir):
        """
        Convert adverse events module to CSV format

        Args:
            study_dir (str): Study directory path

        Returns:
            bool: True if conversion successful, False otherwise
        """
        # Try JSON file first, then fallback to txt file
        ae_json_file = os.path.join(study_dir, "resultsSection_adverseEventsModule.json")
        ae_txt_file = os.path.join(study_dir, "resultsSection_adverseEventsModule.txt")
        csv_file = os.path.join(study_dir, "adverse_events.csv")

        ae_file = ae_json_file if os.path.exists(ae_json_file) else ae_txt_file

        if not os.path.exists(ae_file):
            return False

        try:
            # Load adverse events data
            with open(ae_file, 'r', encoding='utf-8') as f:
                ae_data = json.loads(f.read())

            if not ae_data:
                return False

            # Extract events data (fix the key name from 'adverseEventsGroups' to 'eventGroups')
            groups = ae_data.get('eventGroups', [])
            serious_events = ae_data.get('seriousEvents', [])
            other_events = ae_data.get('otherEvents', [])

            if not groups:
                return False

            # Create CSV even if no events (to show structure)
            return self._create_adverse_events_csv(csv_file, groups, serious_events, other_events)

        except Exception as e:
            print(f"❌ Error processing adverse events for {study_dir}: {e}")
            return False

    def _create_adverse_events_csv(self, csv_file, groups, serious_events, other_events):
        """
        Create CSV file from adverse events data

        Args:
            csv_file (str): CSV file path
            groups (list): Adverse events groups
            serious_events (list): Serious adverse events
            other_events (list): Other adverse events

        Returns:
            bool: True if successful
        """
        try:
            with open(csv_file, 'w', newline='', encoding='utf-8') as csvfile:
                fieldnames = [
                    'event_term', 'event_type', 'organ_system', 'source_vocabulary',
                    'group_id', 'group_title', 'group_description',
                    'subjects_affected', 'subjects_at_risk'
                ]
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()

                # Map group IDs to titles and descriptions
                group_map = self._create_group_mapping(groups)

                # Write serious events
                self._write_events_to_csv(writer, serious_events, group_map, 'Serious')

                # Write other events
                self._write_events_to_csv(writer, other_events, group_map, 'Other')

            return True

        except Exception as e:
            print(f"❌ Error converting adverse events to CSV: {e}")
            return False

    def _create_group_mapping(self, groups):
        """
        Create mapping of group IDs to group information

        Args:
            groups (list): List of adverse events groups

        Returns:
            dict: Mapping of group ID to group information
        """
        return {
            g.get('id'): {
                'title': g.get('title', ''),
                'description': g.get('description', ''),
                'subjects_at_risk': g.get('participantsAffectedNumber', 0)
            } for g in groups
        }

    def _write_events_to_csv(self, writer, events, group_map, event_type):
        """
        Write events data to CSV writer

        Args:
            writer: CSV writer object
            events (list): List of adverse events
            group_map (dict): Group ID to group info mapping
            event_type (str): Type of event ('Serious' or 'Other')
        """
        for event in events:
            event_term = event.get('term', '')
            organ_system = event.get('organSystem', '')
            source_vocabulary = event.get('sourceVocabulary', '')

            for stat in event.get('stats', []):
                group_id = stat.get('groupId', '')
                if group_id in group_map:
                    group_info = group_map[group_id]
                    row = {
                        'event_term': event_term,
                        'event_type': event_type,
                        'organ_system': organ_system,
                        'source_vocabulary': source_vocabulary,
                        'group_id': group_id,
                        'group_title': group_info['title'],
                        'group_description': group_info['description'],
                        'subjects_affected': stat.get('numAffected', 0),
                        'subjects_at_risk': stat.get('numAtRisk', 0)
                    }
                    writer.writerow(row)



    def extract_study_modules(self, nct_id, output_dir, current=None, total=None):
        """
        Extract all modules from a study and save as text files

        Args:
            nct_id (str): NCT ID of the study
            output_dir (str): Output directory path
            current (int, optional): Current study number
            total (int, optional): Total number of studies

        Returns:
            bool: True if extraction successful
        """
        self.report_progress(f"Extracting modules for {nct_id}", nct_id=nct_id, current=current, total=total)

        # Create study directory
        study_dir = os.path.join(output_dir, nct_id)
        os.makedirs(study_dir, exist_ok=True)

        # Get study data from API
        study_data = self.get_study_from_api(nct_id)
        if not study_data:
            self.report_progress(f"❌ Failed to retrieve data for {nct_id}")
            return False

        # Extract modules from all sections
        success = True
        success &= self._extract_protocol_modules(study_data, study_dir)
        success &= self._extract_results_modules(study_data, study_dir)
        success &= self._extract_derived_modules(study_data, study_dir)

        # Process adverse events to CSV
        self.process_adverse_events_to_csv(study_dir)

        return success

    def _extract_protocol_modules(self, study_data, study_dir):
        """
        Extract and save protocol section modules

        Args:
            study_data (dict): Study data from API
            study_dir (str): Study directory path

        Returns:
            bool: True if successful
        """
        success = True
        for module in self.protocol_modules:
            json_data = self.extract_module_data(study_data, 'protocolSection', module)

            # Save both JSON and text versions
            if not self.save_module_json(study_dir, 'protocolSection', module, json_data):
                success = False
            if not self.save_module_text(study_dir, 'protocolSection', module, json_data):
                success = False
        return success

    def _extract_results_modules(self, study_data, study_dir):
        """
        Extract and save results section modules

        Args:
            study_data (dict): Study data from API
            study_dir (str): Study directory path

        Returns:
            bool: True if successful
        """
        success = True
        for module in self.results_modules:
            json_data = self.extract_module_data(study_data, 'resultsSection', module)

            # Save both JSON and text versions
            if not self.save_module_json(study_dir, 'resultsSection', module, json_data):
                success = False
            if not self.save_module_text(study_dir, 'resultsSection', module, json_data):
                success = False
        return success

    def _extract_derived_modules(self, study_data, study_dir):
        """
        Extract and save derived section modules

        Args:
            study_data (dict): Study data from API
            study_dir (str): Study directory path

        Returns:
            bool: True if successful
        """
        success = True
        for module in self.derived_modules:
            if module == 'derivedSection':
                json_data = self.extract_module_data(study_data, module, 'derivedSection')
                if not self.save_module_json(study_dir, module, 'derivedSection', json_data):
                    success = False
                if not self.save_module_text(study_dir, module, 'derivedSection', json_data):
                    success = False
            else:
                json_data = self.extract_module_data(study_data, 'derivedSection', module)
                if not self.save_module_json(study_dir, 'derivedSection', module, json_data):
                    success = False
                if not self.save_module_text(study_dir, 'derivedSection', module, json_data):
                    success = False
        return success

    def extract_studies_for_drug(self, drug_name, nct_ids, output_base_dir="output"):
        """
        Extract modules for all studies of a drug

        Args:
            drug_name (str): Name of the drug
            nct_ids (list): List of NCT IDs to process
            output_base_dir (str): Base output directory

        Returns:
            int: Number of successfully processed studies
        """
        # Create output directory structure (directly under drug folder, no "studies" subfolder)
        drug_dir = os.path.join(output_base_dir, drug_name)
        os.makedirs(drug_dir, exist_ok=True)

        self.report_progress(f"🚀 Starting extraction for {drug_name}", 0, len(nct_ids))

        # Process each study with rate limiting
        success_count = 0
        for i, nct_id in enumerate(nct_ids):
            try:
                success = self.extract_study_modules(nct_id, drug_dir, i+1, len(nct_ids))
                if success:
                    success_count += 1

                # Add delay to respect API rate limits
                time.sleep(RATE_LIMIT_DELAY)

            except Exception as e:
                self.report_progress(f"❌ Error processing {nct_id}: {e}")

        self.report_progress(
            f"✅ Completed extraction for {drug_name}. "
            f"Processed {success_count}/{len(nct_ids)} studies successfully."
        )

        # Generate analysis files (raw variables, meta-variables, characteristics) after all studies are extracted
        if success_count > 0:
            try:
                self.report_progress(f"📊 Generating analysis files for {drug_name}...")
                from meta_analysis_table1 import generate_drug_analysis_files

                result = generate_drug_analysis_files(drug_dir)
                if result['success']:
                    self.report_progress(f"✅ Analysis files generated: {result['studies_processed']} studies analyzed")
                else:
                    self.report_progress(f"⚠️ Analysis files generation failed: {result.get('error', 'Unknown error')}")
            except Exception as e:
                self.report_progress(f"⚠️ Error generating analysis files: {e}")

        return success_count

    def update_progress(self, message, progress=None):
        """
        Update progress for UI integration

        Args:
            message (str): Progress message
            progress (int, optional): Progress percentage
        """
        if self.progress_callback:
            self.progress_callback(message, progress)
        else:
            if progress is not None:
                print(f"{message} - {progress}%")
            else:
                print(message)

    def process_drug(self, drug_name, output_base_dir="output"):
        """
        Process all studies for a drug (legacy method for compatibility)

        Args:
            drug_name (str): Name of the drug to process
            output_base_dir (str): Base output directory

        Returns:
            bool: True if processing successful
        """
        try:
            # Import find_studies dynamically to avoid circular imports
            from find_studies import search_and_filter_studies

            # Get studies for the drug
            self.update_progress(f"🔍 Searching for studies related to {drug_name}...", 5)
            studies = search_and_filter_studies(drug_name)

            if not studies or len(studies) == 0:
                self.update_progress(f"❌ No studies found for {drug_name}", 100)
                return False

            self.update_progress(f"✅ Found {len(studies)} studies for {drug_name}", 10)

            # Extract NCT IDs from studies
            nct_ids = [study.get('nct_id', f'NCT_UNKNOWN_{i}') for i, study in enumerate(studies)]

            # Use the main extraction method
            extracted_count = self.extract_studies_for_drug(drug_name, nct_ids, output_base_dir)

            self.update_progress(
                f"✅ Completed processing {drug_name}. "
                f"Successfully extracted {extracted_count}/{len(studies)} studies.",
                100
            )

            return extracted_count > 0

        except Exception as e:
            self.update_progress(f"❌ Error processing {drug_name}: {str(e)}", 100)
            return False


if __name__ == "__main__":
    """
    Main entry point for command-line usage
    """
    import argparse

    parser = argparse.ArgumentParser(
        description='Clinical Trials Studies Retrieval and Text Extraction',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python retrieve_studies.py canagliflozin
  python retrieve_studies.py "diabetes medication" --output-dir custom_output
        """
    )

    parser.add_argument('drug_name', help='Name of the drug to search for')
    parser.add_argument('--output-dir', default='output',
                       help='Output directory (default: output)')

    args = parser.parse_args()

    print("🔬 Clinical Trials Studies Retrieval System")
    print("=" * 50)
    print(f"💊 Drug Name: {args.drug_name}")
    print(f"📁 Output Directory: {args.output_dir}")
    print("=" * 50)

    # Create extractor and process drug
    extractor = ClinicalTrialsModuleExtractor()

    try:
        success = extractor.process_drug(args.drug_name, args.output_dir)

        if success:
            print("✅ Extraction completed successfully")
        else:
            print("❌ Extraction failed")
            sys.exit(1)

    except KeyboardInterrupt:
        print("\n⚠️ Extraction interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)
