{"populationDescription": "The modified intent-to-treat (mITT) analysis set included all randomized participants who received at least 1 dose of double-blind study agent.", "groups": [{"id": "BG000", "title": "Placebo", "description": "Participants received placebo tablets orally for 26 weeks."}, {"id": "BG001", "title": "Phentermine 15 mg", "description": "Participants received phentermine 15 mg over-encapsulated tablets orally for 26 weeks."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Participants received canagliflozin 300 mg over-encapsulated tablets orally for 26 weeks."}, {"id": "BG003", "title": "Canagliflozin 300 mg/Phentermine 15 mg", "description": "Participants received co-administration of canagliflozin 300 mg and phentermine 15 mg orally for 26 weeks."}, {"id": "BG004", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "82"}, {"groupId": "BG001", "value": "85"}, {"groupId": "BG002", "value": "84"}, {"groupId": "BG003", "value": "83"}, {"groupId": "BG004", "value": "334"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "44.8", "spread": "11.09"}, {"groupId": "BG001", "value": "46.4", "spread": "11.14"}, {"groupId": "BG002", "value": "45.2", "spread": "11.02"}, {"groupId": "BG003", "value": "46.3", "spread": "12.45"}, {"groupId": "BG004", "value": "45.7", "spread": "11.41"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "67"}, {"groupId": "BG001", "value": "69"}, {"groupId": "BG002", "value": "68"}, {"groupId": "BG003", "value": "69"}, {"groupId": "BG004", "value": "273"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "15"}, {"groupId": "BG001", "value": "16"}, {"groupId": "BG002", "value": "16"}, {"groupId": "BG003", "value": "14"}, {"groupId": "BG004", "value": "61"}]}]}]}, {"title": "Region of Enrollment", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "United States", "categories": [{"measurements": [{"groupId": "BG000", "value": "82"}, {"groupId": "BG001", "value": "85"}, {"groupId": "BG002", "value": "84"}, {"groupId": "BG003", "value": "83"}, {"groupId": "BG004", "value": "334"}]}]}]}, {"title": "Baseline Weight", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "Kilogram [kg]", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "104.3", "spread": "18.16"}, {"groupId": "BG001", "value": "102.8", "spread": "17.89"}, {"groupId": "BG002", "value": "103.3", "spread": "19.14"}, {"groupId": "BG003", "value": "101.1", "spread": "18.07"}, {"groupId": "BG004", "value": "102.9", "spread": "18.28"}]}]}]}, {"title": "Baseline Body Mass Index (BMI)", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "kilogram per meter square [kg/m²]", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "38", "spread": "5.2"}, {"groupId": "BG001", "value": "37", "spread": "5.4"}, {"groupId": "BG002", "value": "37.3", "spread": "4.68"}, {"groupId": "BG003", "value": "36.8", "spread": "5.36"}, {"groupId": "BG004", "value": "37.3", "spread": "5.16"}]}]}]}, {"title": "Baseline Systolic Blood Pressure", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "millimeters of mercury [mmHg]", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "122.5", "spread": "13.86"}, {"groupId": "BG001", "value": "123.0", "spread": "11.83"}, {"groupId": "BG002", "value": "124.5", "spread": "13.01"}, {"groupId": "BG003", "value": "124.8", "spread": "12.83"}, {"groupId": "BG004", "value": "123.7", "spread": "12.87"}]}]}]}, {"title": "Baseline Diastolic Blood Pressure", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "millimeters of mercury [mmHg]", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "78.8", "spread": "8.43"}, {"groupId": "BG001", "value": "78.1", "spread": "9.00"}, {"groupId": "BG002", "value": "80.2", "spread": "7.88"}, {"groupId": "BG003", "value": "79.4", "spread": "8.21"}, {"groupId": "BG004", "value": "79.1", "spread": "8.39"}]}]}]}, {"title": "Baseline Pulse Rate", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "(BEATS/MIN)", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "73.5", "spread": "8.72"}, {"groupId": "BG001", "value": "70.7", "spread": "10.09"}, {"groupId": "BG002", "value": "71.5", "spread": "9.40"}, {"groupId": "BG003", "value": "72.4", "spread": "9.66"}, {"groupId": "BG004", "value": "72.0", "spread": "9.50"}]}]}]}]}