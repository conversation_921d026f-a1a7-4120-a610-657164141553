{"preAssignmentDetails": "Between screening and randomisation, eligible subjects were included in a 4-week single-blind run-in period in which all subjects were placed on a hypocaloric diet.", "groups": [{"id": "FG000", "title": "Placebo", "description": "Participants received placebo tablets orally for 26 weeks."}, {"id": "FG001", "title": "Phentermine 15 mg", "description": "Participants received phentermine 15 mg over-encapsulated tablets orally for 26 weeks."}, {"id": "FG002", "title": "Canagliflozin 300 mg", "description": "Participants received canagliflozin 300 mg over-encapsulated tablets orally for 26 weeks."}, {"id": "FG003", "title": "Canagliflozin 300 mg/Phentermine 15 mg", "description": "Participants received co-administration of canagliflozin 300 mg and phentermine 15 mg orally for 26 weeks."}], "periods": [{"title": "Overall Study", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "82"}, {"groupId": "FG001", "numSubjects": "85"}, {"groupId": "FG002", "numSubjects": "84"}, {"groupId": "FG003", "numSubjects": "84"}]}, {"type": "Treated", "achievements": [{"groupId": "FG000", "numSubjects": "82"}, {"groupId": "FG001", "numSubjects": "85"}, {"groupId": "FG002", "numSubjects": "84"}, {"groupId": "FG003", "numSubjects": "83"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "57"}, {"groupId": "FG001", "numSubjects": "60"}, {"groupId": "FG002", "numSubjects": "53"}, {"groupId": "FG003", "numSubjects": "61"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "25"}, {"groupId": "FG001", "numSubjects": "25"}, {"groupId": "FG002", "numSubjects": "31"}, {"groupId": "FG003", "numSubjects": "23"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "5"}, {"groupId": "FG001", "numSubjects": "6"}, {"groupId": "FG002", "numSubjects": "10"}, {"groupId": "FG003", "numSubjects": "4"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "9"}, {"groupId": "FG001", "numSubjects": "9"}, {"groupId": "FG002", "numSubjects": "14"}, {"groupId": "FG003", "numSubjects": "11"}]}, {"type": "Protocol Violation", "reasons": [{"groupId": "FG000", "numSubjects": "3"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "2"}, {"groupId": "FG003", "numSubjects": "1"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "7"}, {"groupId": "FG001", "numSubjects": "6"}, {"groupId": "FG002", "numSubjects": "4"}, {"groupId": "FG003", "numSubjects": "4"}]}, {"type": "Other", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "3"}, {"groupId": "FG002", "numSubjects": "1"}, {"groupId": "FG003", "numSubjects": "2"}]}, {"type": "Randomized but not received treatment", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}]}]}]}