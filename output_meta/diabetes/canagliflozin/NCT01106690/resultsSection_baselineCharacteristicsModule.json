{"groups": [{"id": "BG000", "title": "Placebo/Sitagliptin", "description": "Each patient received matching placebo once daily for 26 weeks with stable doses of metformin and pioglitazone. At Week 26, patients were switched from placebo to 100 mg of sitagliptin once daily with stable doses of metformin and pioglitazone until Week 52."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin once daily for 52 weeks with stable doses of metformin and pioglitazone."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once daily for 52 weeks with stable doses of metformin and pioglitazone."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "115"}, {"groupId": "BG001", "value": "113"}, {"groupId": "BG002", "value": "114"}, {"groupId": "BG003", "value": "342"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "83"}, {"groupId": "BG001", "value": "83"}, {"groupId": "BG002", "value": "83"}, {"groupId": "BG003", "value": "249"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "32"}, {"groupId": "BG001", "value": "30"}, {"groupId": "BG002", "value": "31"}, {"groupId": "BG003", "value": "93"}]}]}]}, {"title": "Age Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "58.3", "spread": "9.56"}, {"groupId": "BG001", "value": "56.7", "spread": "10.36"}, {"groupId": "BG002", "value": "57", "spread": "10.19"}, {"groupId": "BG003", "value": "57.4", "spread": "10.03"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "39"}, {"groupId": "BG001", "value": "36"}, {"groupId": "BG002", "value": "51"}, {"groupId": "BG003", "value": "126"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "76"}, {"groupId": "BG001", "value": "77"}, {"groupId": "BG002", "value": "63"}, {"groupId": "BG003", "value": "216"}]}]}]}, {"title": "Region of Enrollment", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "24"}, {"groupId": "BG001", "value": "22"}, {"groupId": "BG002", "value": "21"}, {"groupId": "BG003", "value": "67"}]}]}, {"title": "FINLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "13"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "2"}]}]}, {"title": "GERMANY", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "7"}, {"groupId": "BG003", "value": "19"}]}]}, {"title": "GREECE", "categories": [{"measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "1"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "10"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "25"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "21"}]}]}, {"title": "SPAIN", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "15"}]}]}, {"title": "THAILAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "17"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "8"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "43"}, {"groupId": "BG001", "value": "55"}, {"groupId": "BG002", "value": "56"}, {"groupId": "BG003", "value": "154"}]}]}]}]}