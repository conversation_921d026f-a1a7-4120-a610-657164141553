{"populationDescription": "All randomized participants who received at least one dose of study drug.", "groups": [{"id": "BG000", "title": "1.5 mg Dulaglutide", "description": "Dulaglutide 1.5 milligrams (mg) given SC QW for 24 weeks."}, {"id": "BG001", "title": "0.75 mg Dulaglutide", "description": "Dulaglutide 0.75 mg given subcutaneously (SC) once a week (QW) for 24 weeks."}, {"id": "BG002", "title": "Placebo", "description": "Placebo given SC QW for 24 weeks."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "56.17", "spread": "9.26"}, {"groupId": "BG001", "value": "58.55", "spread": "9.14"}, {"groupId": "BG002", "value": "57.10", "spread": "9.59"}, {"groupId": "BG003", "value": "57.27", "spread": "9.36"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "calculatePct": false, "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "65"}, {"groupId": "BG001", "value": "72"}, {"groupId": "BG002", "value": "74"}, {"groupId": "BG003", "value": "211"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "77"}, {"groupId": "BG001", "value": "69"}, {"groupId": "BG002", "value": "66"}, {"groupId": "BG003", "value": "212"}]}]}]}, {"title": "Ethnicity (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "calculatePct": false, "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"title": "Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "51"}, {"groupId": "BG001", "value": "44"}, {"groupId": "BG002", "value": "44"}, {"groupId": "BG003", "value": "139"}]}, {"title": "Not Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "90"}, {"groupId": "BG001", "value": "97"}, {"groupId": "BG002", "value": "94"}, {"groupId": "BG003", "value": "281"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "3"}]}]}]}, {"title": "Race (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "calculatePct": false, "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"title": "American Indian or Alaska Native", "measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "7"}]}, {"title": "Asian", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "1"}]}, {"title": "Native Hawaiian or Other Pacific Islander", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Black or African American", "measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "12"}]}, {"title": "White", "measurements": [{"groupId": "BG000", "value": "127"}, {"groupId": "BG001", "value": "127"}, {"groupId": "BG002", "value": "124"}, {"groupId": "BG003", "value": "378"}]}, {"title": "More than one race", "measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "25"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}]}]}, {"title": "Region of Enrollment", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "calculatePct": false, "classes": [{"title": "Austria", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "9"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "29"}]}]}, {"title": "Hungary", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "19"}, {"groupId": "BG001", "value": "19"}, {"groupId": "BG002", "value": "19"}, {"groupId": "BG003", "value": "57"}]}]}, {"title": "United States", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "30"}, {"groupId": "BG001", "value": "30"}, {"groupId": "BG002", "value": "28"}, {"groupId": "BG003", "value": "88"}]}]}, {"title": "Czechia", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "25"}, {"groupId": "BG001", "value": "23"}, {"groupId": "BG002", "value": "25"}, {"groupId": "BG003", "value": "73"}]}]}, {"title": "Mexico", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "27"}, {"groupId": "BG001", "value": "28"}, {"groupId": "BG002", "value": "28"}, {"groupId": "BG003", "value": "83"}]}]}, {"title": "Israel", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "6"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "18"}]}]}, {"title": "Germany", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "12"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "10"}, {"groupId": "BG003", "value": "33"}]}]}, {"title": "Spain", "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "142"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "140"}, {"groupId": "BG003", "value": "423"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "14"}, {"groupId": "BG001", "value": "13"}, {"groupId": "BG002", "value": "15"}, {"groupId": "BG003", "value": "42"}]}]}]}, {"title": "Baseline Mean Hemoglobin A1c (HbA1c) Treatment-Regimen Estimand", "populationDescription": "All randomized participants who received at least one dose of study drug and had a baseline measure of HbA1c.", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "percentage of HbA1c", "classes": [{"denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "140"}, {"groupId": "BG001", "value": "139"}, {"groupId": "BG002", "value": "137"}, {"groupId": "BG003", "value": "416"}]}], "categories": [{"measurements": [{"groupId": "BG000", "value": "8.04", "spread": "0.66"}, {"groupId": "BG001", "value": "8.04", "spread": "0.61"}, {"groupId": "BG002", "value": "8.05", "spread": "0.66"}, {"groupId": "BG003", "value": "8.04", "spread": "0.64"}]}]}]}]}