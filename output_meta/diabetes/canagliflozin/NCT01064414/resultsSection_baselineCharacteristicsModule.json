{"groups": [{"id": "BG000", "title": "Placebo", "description": "Each patient received matching placebo once daily for 52 weeks."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin once daily for 52 weeks."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once daily for 52 weeks."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "90"}, {"groupId": "BG001", "value": "90"}, {"groupId": "BG002", "value": "89"}, {"groupId": "BG003", "value": "269"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "27"}, {"groupId": "BG001", "value": "24"}, {"groupId": "BG002", "value": "32"}, {"groupId": "BG003", "value": "83"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "63"}, {"groupId": "BG001", "value": "66"}, {"groupId": "BG002", "value": "57"}, {"groupId": "BG003", "value": "186"}]}]}]}, {"title": "Age Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "68.2", "spread": "8.4"}, {"groupId": "BG001", "value": "69.5", "spread": "8.2"}, {"groupId": "BG002", "value": "67.9", "spread": "8.24"}, {"groupId": "BG003", "value": "68.5", "spread": "8.28"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "33"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "41"}, {"groupId": "BG003", "value": "106"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "57"}, {"groupId": "BG001", "value": "58"}, {"groupId": "BG002", "value": "48"}, {"groupId": "BG003", "value": "163"}]}]}]}, {"title": "Region of Enrollment", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "AUSTRALIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "14"}]}]}, {"title": "BELGIUM", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "12"}]}]}, {"title": "BRAZIL", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "4"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "13"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "21"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "16"}]}]}, {"title": "GERMANY", "categories": [{"measurements": [{"groupId": "BG000", "value": "6"}, {"groupId": "BG001", "value": "10"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "18"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "9"}]}]}, {"title": "ITALY", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "6"}]}]}, {"title": "LATVIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "7"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "4"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "14"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "4"}]}]}, {"title": "NEW ZEALAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "14"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "14"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "5"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "30"}]}]}, {"title": "SOUTH AFRICA", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "6"}]}]}, {"title": "SOUTH KOREA", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "2"}]}]}, {"title": "SPAIN", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "17"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "16"}, {"groupId": "BG001", "value": "14"}, {"groupId": "BG002", "value": "17"}, {"groupId": "BG003", "value": "47"}]}]}]}]}