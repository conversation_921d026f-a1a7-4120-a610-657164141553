{"groups": [{"id": "BG000", "title": "Placebo (Placebo)", "description": "Participants received one capsule of matching placebo orally once daily for the duration of the study or until early discontinuation from treatment."}, {"id": "BG001", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (Experimental)", "description": "Participants received canagliflozin (JNJ-28431754) 100 milligram (mg) once daily during the first 13 weeks, then the dose was increased to 300 mg once daily (if the participant required additional glycemic control, provided the 100-mg dose was well tolerated)."}, {"id": "BG002", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "2905"}, {"groupId": "BG001", "value": "2907"}, {"groupId": "BG002", "value": "5812"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "64", "spread": "8.28"}, {"groupId": "BG001", "value": "63.9", "spread": "8.42"}, {"groupId": "BG002", "value": "64", "spread": "8.35"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "1111"}, {"groupId": "BG001", "value": "1053"}, {"groupId": "BG002", "value": "2164"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "1794"}, {"groupId": "BG001", "value": "1854"}, {"groupId": "BG002", "value": "3648"}]}]}]}, {"title": "Ethnicity (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "586"}, {"groupId": "BG001", "value": "604"}, {"groupId": "BG002", "value": "1190"}]}, {"title": "Not Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "2303"}, {"groupId": "BG001", "value": "2290"}, {"groupId": "BG002", "value": "4593"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "16"}, {"groupId": "BG001", "value": "13"}, {"groupId": "BG002", "value": "29"}]}]}]}, {"title": "Race (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "American Indian or Alaska Native", "measurements": [{"groupId": "BG000", "value": "19"}, {"groupId": "BG001", "value": "14"}, {"groupId": "BG002", "value": "33"}]}, {"title": "Asian", "measurements": [{"groupId": "BG000", "value": "245"}, {"groupId": "BG001", "value": "244"}, {"groupId": "BG002", "value": "489"}]}, {"title": "Native Hawaiian or Other Pacific Islander", "measurements": [{"groupId": "BG000", "value": "09"}, {"groupId": "BG001", "value": "08"}, {"groupId": "BG002", "value": "17"}]}, {"title": "Black or African American", "measurements": [{"groupId": "BG000", "value": "125"}, {"groupId": "BG001", "value": "106"}, {"groupId": "BG002", "value": "231"}]}, {"title": "White", "measurements": [{"groupId": "BG000", "value": "2372"}, {"groupId": "BG001", "value": "2393"}, {"groupId": "BG002", "value": "4765"}]}, {"title": "More than one race", "measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "09"}, {"groupId": "BG002", "value": "19"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "125"}, {"groupId": "BG001", "value": "133"}, {"groupId": "BG002", "value": "258"}]}]}]}, {"title": "Race/Ethnicity, Customized", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"title": "Asian", "categories": [{"measurements": [{"groupId": "BG000", "value": "245"}, {"groupId": "BG001", "value": "244"}, {"groupId": "BG002", "value": "489"}]}]}, {"title": "Black or African American", "categories": [{"measurements": [{"groupId": "BG000", "value": "125"}, {"groupId": "BG001", "value": "106"}, {"groupId": "BG002", "value": "231"}]}]}, {"title": "Hispanic or Latino", "categories": [{"measurements": [{"groupId": "BG000", "value": "407"}, {"groupId": "BG001", "value": "437"}, {"groupId": "BG002", "value": "844"}]}]}, {"title": "Other", "categories": [{"measurements": [{"groupId": "BG000", "value": "174"}, {"groupId": "BG001", "value": "172"}, {"groupId": "BG002", "value": "346"}]}]}, {"title": "White Non-Hispanic", "categories": [{"measurements": [{"groupId": "BG000", "value": "1954"}, {"groupId": "BG001", "value": "1948"}, {"groupId": "BG002", "value": "3902"}]}]}]}, {"title": "Region of Enrollment", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"title": "ARGENTINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "146"}, {"groupId": "BG001", "value": "159"}, {"groupId": "BG002", "value": "305"}]}]}, {"title": "AUSTRALIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "58"}, {"groupId": "BG001", "value": "51"}, {"groupId": "BG002", "value": "109"}]}]}, {"title": "BELGIUM", "categories": [{"measurements": [{"groupId": "BG000", "value": "86"}, {"groupId": "BG001", "value": "66"}, {"groupId": "BG002", "value": "152"}]}]}, {"title": "BRAZIL", "categories": [{"measurements": [{"groupId": "BG000", "value": "277"}, {"groupId": "BG001", "value": "272"}, {"groupId": "BG002", "value": "549"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "136"}, {"groupId": "BG001", "value": "146"}, {"groupId": "BG002", "value": "282"}]}]}, {"title": "CHINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "46"}, {"groupId": "BG001", "value": "46"}, {"groupId": "BG002", "value": "92"}]}]}, {"title": "CZECH REPUBLIC", "categories": [{"measurements": [{"groupId": "BG000", "value": "67"}, {"groupId": "BG001", "value": "72"}, {"groupId": "BG002", "value": "139"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "69"}, {"groupId": "BG001", "value": "55"}, {"groupId": "BG002", "value": "124"}]}]}, {"title": "GERMANY", "categories": [{"measurements": [{"groupId": "BG000", "value": "46"}, {"groupId": "BG001", "value": "55"}, {"groupId": "BG002", "value": "101"}]}]}, {"title": "HUNGARY", "categories": [{"measurements": [{"groupId": "BG000", "value": "82"}, {"groupId": "BG001", "value": "97"}, {"groupId": "BG002", "value": "179"}]}]}, {"title": "ITALY", "categories": [{"measurements": [{"groupId": "BG000", "value": "49"}, {"groupId": "BG001", "value": "49"}, {"groupId": "BG002", "value": "98"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "50"}, {"groupId": "BG001", "value": "42"}, {"groupId": "BG002", "value": "92"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "118"}, {"groupId": "BG001", "value": "110"}, {"groupId": "BG002", "value": "228"}]}]}, {"title": "NETHERLANDS", "categories": [{"measurements": [{"groupId": "BG000", "value": "117"}, {"groupId": "BG001", "value": "132"}, {"groupId": "BG002", "value": "249"}]}]}, {"title": "NEW ZEALAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "49"}, {"groupId": "BG001", "value": "56"}, {"groupId": "BG002", "value": "105"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "186"}, {"groupId": "BG001", "value": "177"}, {"groupId": "BG002", "value": "363"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "213"}, {"groupId": "BG001", "value": "199"}, {"groupId": "BG002", "value": "412"}]}]}, {"title": "SOUTH KOREA", "categories": [{"measurements": [{"groupId": "BG000", "value": "73"}, {"groupId": "BG001", "value": "94"}, {"groupId": "BG002", "value": "167"}]}]}, {"title": "SPAIN", "categories": [{"measurements": [{"groupId": "BG000", "value": "247"}, {"groupId": "BG001", "value": "249"}, {"groupId": "BG002", "value": "496"}]}]}, {"title": "SWEDEN", "categories": [{"measurements": [{"groupId": "BG000", "value": "118"}, {"groupId": "BG001", "value": "103"}, {"groupId": "BG002", "value": "221"}]}]}, {"title": "TAIWAN", "categories": [{"measurements": [{"groupId": "BG000", "value": "44"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "76"}]}]}, {"title": "UKRAINE", "categories": [{"measurements": [{"groupId": "BG000", "value": "213"}, {"groupId": "BG001", "value": "236"}, {"groupId": "BG002", "value": "449"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "78"}, {"groupId": "BG001", "value": "73"}, {"groupId": "BG002", "value": "151"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "337"}, {"groupId": "BG001", "value": "336"}, {"groupId": "BG002", "value": "673"}]}]}]}]}