{"groups": [{"id": "BG000", "title": "Metformin XR", "description": "Participants received metformin extended release (XR) tablets (in doses titrated over 9 weeks) once daily with the evening meal, plus one placebo capsule before the morning meal and one placebo capsule with the evening meal (to match the canagliflozin capsules administered in other treatment arms) for 26 weeks."}, {"id": "BG001", "title": "Canagliflozin 100 Milligram (mg)", "description": "Participants received one 100 milligram (mg) canagliflozin capsule before the morning meal and one matching placebo capsule with the evening meal plus placebo tablets with the evening meal (to match the metformin XR tablets administered in other treatment arms) for 26 weeks."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Participants received one 300 mg canagliflozin capsule before the morning meal and one matching placebo capsule with the evening meal plus placebo tablets with the evening meal (to match the metformin XR tablets administered in other treatment arms) for 26 weeks."}, {"id": "BG003", "title": "Canagliflozin 100 mg + Metformin XR", "description": "Participants received one 100 mg canagliflozin capsule with the evening meal and one matching placebo capsule before the morning meal plus metformin XR tablets (in doses titrated over 9 weeks) once daily with the evening meal for 26 weeks."}, {"id": "BG004", "title": "Canagliflozin 300 mg + Metformin XR", "description": "Participants received one 300 mg canagliflozin capsule with the evening meal and one matching placebo capsule before the morning meal plus metformin XR tablets (in doses titrated over 9 weeks) once daily with the evening meal for 26 weeks."}, {"id": "BG005", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "237"}, {"groupId": "BG001", "value": "237"}, {"groupId": "BG002", "value": "238"}, {"groupId": "BG003", "value": "237"}, {"groupId": "BG004", "value": "237"}, {"groupId": "BG005", "value": "1186"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "55.2", "spread": "9.75"}, {"groupId": "BG001", "value": "54.0", "spread": "10.70"}, {"groupId": "BG002", "value": "55.8", "spread": "9.56"}, {"groupId": "BG003", "value": "54.2", "spread": "9.58"}, {"groupId": "BG004", "value": "55.4", "spread": "9.84"}, {"groupId": "BG005", "value": "54.9", "spread": "9.91"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "121"}, {"groupId": "BG001", "value": "132"}, {"groupId": "BG002", "value": "113"}, {"groupId": "BG003", "value": "129"}, {"groupId": "BG004", "value": "122"}, {"groupId": "BG005", "value": "617"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "116"}, {"groupId": "BG001", "value": "105"}, {"groupId": "BG002", "value": "125"}, {"groupId": "BG003", "value": "108"}, {"groupId": "BG004", "value": "115"}, {"groupId": "BG005", "value": "569"}]}]}]}, {"title": "Region of Enrollment", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "Argentina", "categories": [{"measurements": [{"groupId": "BG000", "value": "12"}, {"groupId": "BG001", "value": "19"}, {"groupId": "BG002", "value": "21"}, {"groupId": "BG003", "value": "17"}, {"groupId": "BG004", "value": "20"}, {"groupId": "BG005", "value": "89"}]}]}, {"title": "Brazil", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "0"}, {"groupId": "BG004", "value": "0"}, {"groupId": "BG005", "value": "4"}]}]}, {"title": "Czech Republic", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "6"}, {"groupId": "BG004", "value": "8"}, {"groupId": "BG005", "value": "34"}]}]}, {"title": "Hungary", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "9"}, {"groupId": "BG004", "value": "4"}, {"groupId": "BG005", "value": "28"}]}]}, {"title": "Mexico", "categories": [{"measurements": [{"groupId": "BG000", "value": "33"}, {"groupId": "BG001", "value": "31"}, {"groupId": "BG002", "value": "19"}, {"groupId": "BG003", "value": "38"}, {"groupId": "BG004", "value": "38"}, {"groupId": "BG005", "value": "159"}]}]}, {"title": "Romania", "categories": [{"measurements": [{"groupId": "BG000", "value": "24"}, {"groupId": "BG001", "value": "16"}, {"groupId": "BG002", "value": "21"}, {"groupId": "BG003", "value": "27"}, {"groupId": "BG004", "value": "14"}, {"groupId": "BG005", "value": "102"}]}]}, {"title": "Russian Federation", "categories": [{"measurements": [{"groupId": "BG000", "value": "38"}, {"groupId": "BG001", "value": "38"}, {"groupId": "BG002", "value": "45"}, {"groupId": "BG003", "value": "44"}, {"groupId": "BG004", "value": "37"}, {"groupId": "BG005", "value": "202"}]}]}, {"title": "Slovakia", "categories": [{"measurements": [{"groupId": "BG000", "value": "17"}, {"groupId": "BG001", "value": "12"}, {"groupId": "BG002", "value": "22"}, {"groupId": "BG003", "value": "9"}, {"groupId": "BG004", "value": "9"}, {"groupId": "BG005", "value": "69"}]}]}, {"title": "South Africa", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "5"}, {"groupId": "BG004", "value": "3"}, {"groupId": "BG005", "value": "22"}]}]}, {"title": "South Korea", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "1"}, {"groupId": "BG004", "value": "3"}, {"groupId": "BG005", "value": "12"}]}]}, {"title": "Ukraine", "categories": [{"measurements": [{"groupId": "BG000", "value": "50"}, {"groupId": "BG001", "value": "56"}, {"groupId": "BG002", "value": "42"}, {"groupId": "BG003", "value": "40"}, {"groupId": "BG004", "value": "54"}, {"groupId": "BG005", "value": "242"}]}]}, {"title": "United States", "categories": [{"measurements": [{"groupId": "BG000", "value": "43"}, {"groupId": "BG001", "value": "45"}, {"groupId": "BG002", "value": "47"}, {"groupId": "BG003", "value": "41"}, {"groupId": "BG004", "value": "47"}, {"groupId": "BG005", "value": "223"}]}]}]}]}