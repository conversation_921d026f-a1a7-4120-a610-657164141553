{"eligibilityCriteria": "Inclusion Criteria:\n\n* Type 2 diabetes mellitus\n* Inadequate glucose control while using metformin monotherapy (MET) for at least 8 weeks at stable daily doses of at least 1500 milligram (mg) before screening visit (Visit 1)\n\n  a. Hemoglobin A1c (HbA1c) equal to (=) 7.5 percent (%) to 10.5% at Visit 1\n* Adequate qualifying continuous glucose monitoring (CGM) reading during the pre-randomization (selection) phase\n* Estimated glomerular filtration rate (eGFR) of at least 60 milliliter/minute (mL/min)/1.73 meter square (m\\^2) at Visit 1\n* Body mass index of 22 through 45 kilogram per meter square (kg/m\\^2) at Visit 1\n\nExclusion Criteria:\n\n* History of any of the following (at Visit 1):\n\n  1. Diabetic ketoacidosis (DKA)\n  2. Type 1 diabetes mellitus (T1DM)\n  3. Pancreatic (for example, Beta-islet cell) transplantation\n  4. Diabetes secondary to pancreatitis or pancreatectomy\n  5. Personal history of, or ongoing, pancreatitis\n  6. One or more episodes of severe hypoglycemia (requiring assistance from others), as documented in the history obtained at Visit 1\n  7. Hereditary glucose-galactose malabsorption or primary renal glucosuria\n* Repeated fasting plasma glucose (FPG) or fasting self-monitored blood glucose (SMBG) greater than (\\>) 270 milligram per deciliter (mg/dL) during the pre-treatment phase\n* Treatment with any other oral or parenteral antidiabetic medications different from metformin monotherapy, including but not limited to Dipeptidyl peptidase-4 (DPP-4) inhibitors, Sulphonylureas, thiazolidinediones, insulins and Glucagon-like peptide-1 receptor agonist (GLP-1RAs); Sodium-glucose co-transporter 2 (SGLT-2) inhibitors and investigational agents\n* Received an investigational drug or vaccine or used an invasive investigational medical device within 30 days before the planned first dose of study drug\n* Current use of \"natural medicines\" or natural medicinal products for diabetes (for example, cactus-derived nutrients, celery)", "healthyVolunteers": false, "sex": "ALL", "minimumAge": "19 Years", "maximumAge": "54 Years", "stdAges": ["ADULT"]}