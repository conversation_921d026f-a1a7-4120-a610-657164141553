{"groups": [{"id": "BG000", "title": "Placebo/Sitagliptin", "description": "Each patient received matching placebo once daily for 26 weeks and were then switched from placebo to 100 mg of sitagliptin once daily until Week 52. Placebo and sitagliptin were given with protocol-specified doses of metformin immediate release."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin immediate release."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin immediate release."}, {"id": "BG003", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once daily for 52 weeks with protocol-specified doses of metformin immediate release."}, {"id": "BG004", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "183"}, {"groupId": "BG001", "value": "368"}, {"groupId": "BG002", "value": "367"}, {"groupId": "BG003", "value": "366"}, {"groupId": "BG004", "value": "1284"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}, {"groupId": "BG004", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "146"}, {"groupId": "BG001", "value": "314"}, {"groupId": "BG002", "value": "309"}, {"groupId": "BG003", "value": "309"}, {"groupId": "BG004", "value": "1078"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "37"}, {"groupId": "BG001", "value": "54"}, {"groupId": "BG002", "value": "58"}, {"groupId": "BG003", "value": "57"}, {"groupId": "BG004", "value": "206"}]}]}]}, {"title": "Age Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "55.3", "spread": "9.76"}, {"groupId": "BG001", "value": "55.5", "spread": "9.38"}, {"groupId": "BG002", "value": "55.3", "spread": "9.19"}, {"groupId": "BG003", "value": "55.5", "spread": "9.55"}, {"groupId": "BG004", "value": "55.4", "spread": "9.42"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "89"}, {"groupId": "BG001", "value": "194"}, {"groupId": "BG002", "value": "202"}, {"groupId": "BG003", "value": "194"}, {"groupId": "BG004", "value": "679"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "94"}, {"groupId": "BG001", "value": "174"}, {"groupId": "BG002", "value": "165"}, {"groupId": "BG003", "value": "172"}, {"groupId": "BG004", "value": "605"}]}]}]}, {"title": "Region Enroll", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "ARGENTINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "9"}, {"groupId": "BG002", "value": "15"}, {"groupId": "BG003", "value": "11"}, {"groupId": "BG004", "value": "43"}]}]}, {"title": "BULGARIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "3"}, {"groupId": "BG004", "value": "10"}]}]}, {"title": "COLOMBIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "15"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "15"}, {"groupId": "BG004", "value": "42"}]}]}, {"title": "CZECH REPUBLIC", "categories": [{"measurements": [{"groupId": "BG000", "value": "7"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "7"}, {"groupId": "BG004", "value": "30"}]}]}, {"title": "ESTONIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "6"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "5"}, {"groupId": "BG004", "value": "20"}]}]}, {"title": "GREECE", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "4"}, {"groupId": "BG004", "value": "9"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "13"}, {"groupId": "BG001", "value": "28"}, {"groupId": "BG002", "value": "31"}, {"groupId": "BG003", "value": "22"}, {"groupId": "BG004", "value": "94"}]}]}, {"title": "ITALY", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "1"}, {"groupId": "BG004", "value": "9"}]}]}, {"title": "LATVIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "10"}, {"groupId": "BG004", "value": "26"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "9"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "7"}, {"groupId": "BG004", "value": "34"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "12"}, {"groupId": "BG001", "value": "20"}, {"groupId": "BG002", "value": "26"}, {"groupId": "BG003", "value": "18"}, {"groupId": "BG004", "value": "76"}]}]}, {"title": "PERU", "categories": [{"measurements": [{"groupId": "BG000", "value": "16"}, {"groupId": "BG001", "value": "36"}, {"groupId": "BG002", "value": "30"}, {"groupId": "BG003", "value": "37"}, {"groupId": "BG004", "value": "119"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "18"}, {"groupId": "BG002", "value": "14"}, {"groupId": "BG003", "value": "10"}, {"groupId": "BG004", "value": "43"}]}]}, {"title": "PORTUGAL", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "2"}, {"groupId": "BG004", "value": "4"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "15"}, {"groupId": "BG001", "value": "34"}, {"groupId": "BG002", "value": "28"}, {"groupId": "BG003", "value": "22"}, {"groupId": "BG004", "value": "99"}]}]}, {"title": "SINGAPORE", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "3"}, {"groupId": "BG004", "value": "14"}]}]}, {"title": "SLOVAKIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "12"}, {"groupId": "BG001", "value": "16"}, {"groupId": "BG002", "value": "21"}, {"groupId": "BG003", "value": "17"}, {"groupId": "BG004", "value": "66"}]}]}, {"title": "SWEDEN", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "4"}, {"groupId": "BG004", "value": "12"}]}]}, {"title": "THAILAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "9"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "8"}, {"groupId": "BG004", "value": "32"}]}]}, {"title": "TURKEY", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "9"}, {"groupId": "BG004", "value": "26"}]}]}, {"title": "UKRAINE", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "24"}, {"groupId": "BG002", "value": "29"}, {"groupId": "BG003", "value": "30"}, {"groupId": "BG004", "value": "91"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "50"}, {"groupId": "BG001", "value": "111"}, {"groupId": "BG002", "value": "103"}, {"groupId": "BG003", "value": "121"}, {"groupId": "BG004", "value": "385"}]}]}]}]}