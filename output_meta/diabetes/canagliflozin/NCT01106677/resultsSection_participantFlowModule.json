{"preAssignmentDetails": "1,284 patients were randomly allocated to the 4 treatment arms. All patients received at least 1 dose of study drug and were included in the modified intent-to-treat (mITT) analysis set (used for the Week 26 and week 52 efficacy analyses). All 1,284 patients were included in the Week 26 and Week 52 safety analysis sets.", "recruitmentDetails": "This study evaluated the efficacy and safety of canagliflozin compared with sitagliptin and placebo in patients with type 2 diabetes mellitus with inadequate control despite treatment with metformin. The study was conducted between 07 April 2010 and 17 August 2012 and recruited patients from 169 study centers in 22 countries worldwide.", "groups": [{"id": "FG000", "title": "Placebo/Sitagliptin", "description": "Each patient received matching placebo once daily for 26 weeks and were then switched from placebo to 100 mg of sitagliptin once daily until Week 52. Placebo and sitagliptin were given with protocol-specified doses of metformin immediate release."}, {"id": "FG001", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin immediate release."}, {"id": "FG002", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin immediate release."}, {"id": "FG003", "title": "Sitagliptin 100 mg", "description": "Each patient received 100 mg of sitagliptin once daily for 52 weeks with protocol-specified doses of metformin immediate release."}], "periods": [{"title": "Core Period: Baseline to Week 26", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "183"}, {"groupId": "FG001", "numSubjects": "368"}, {"groupId": "FG002", "numSubjects": "367"}, {"groupId": "FG003", "numSubjects": "366"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "155"}, {"groupId": "FG001", "numSubjects": "322"}, {"groupId": "FG002", "numSubjects": "323"}, {"groupId": "FG003", "numSubjects": "319"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "28"}, {"groupId": "FG001", "numSubjects": "46"}, {"groupId": "FG002", "numSubjects": "44"}, {"groupId": "FG003", "numSubjects": "47"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "7"}, {"groupId": "FG001", "numSubjects": "18"}, {"groupId": "FG002", "numSubjects": "6"}, {"groupId": "FG003", "numSubjects": "8"}]}, {"type": "Death", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "1"}, {"groupId": "FG003", "numSubjects": "0"}]}, {"type": "Lack of Efficacy", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "0"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "3"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "6"}, {"groupId": "FG003", "numSubjects": "3"}]}, {"type": "Physician Decision", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "3"}, {"groupId": "FG003", "numSubjects": "1"}]}, {"type": "Protocol Violation", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "3"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "5"}, {"groupId": "FG001", "numSubjects": "3"}, {"groupId": "FG002", "numSubjects": "15"}, {"groupId": "FG003", "numSubjects": "6"}]}, {"type": "Creatinine or eGFR withdrawal criteria", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "2"}, {"groupId": "FG002", "numSubjects": "2"}, {"groupId": "FG003", "numSubjects": "3"}]}, {"type": "Noncompliance with study drug", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "3"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "0"}]}, {"type": "Study terminated by sponsor", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "2"}, {"groupId": "FG003", "numSubjects": "0"}]}, {"type": "Product quality complaint", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "1"}, {"groupId": "FG003", "numSubjects": "0"}]}, {"type": "Other", "reasons": [{"groupId": "FG000", "numSubjects": "8"}, {"groupId": "FG001", "numSubjects": "14"}, {"groupId": "FG002", "numSubjects": "8"}, {"groupId": "FG003", "numSubjects": "23"}]}, {"type": "Pregnancy", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "0"}]}]}, {"title": "Extension Period: Week 26 to Week 52", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "comment": "2 pts completed core but did not enter ext: protcol violation(1), lost to f/u(1).", "numSubjects": "153"}, {"groupId": "FG001", "comment": "6 pts completed core but did not enter ext: not specified(5), withdrawal by subject(1).", "numSubjects": "316"}, {"groupId": "FG002", "comment": "2 pts completed core but did not enter ext: not specified(1), withdrawal by subject(1).", "numSubjects": "321"}, {"groupId": "FG003", "comment": "6 pts completed core but did not enter ext: not specified(3), physician decision(2), lost to f/u(1).", "numSubjects": "313"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "138"}, {"groupId": "FG001", "numSubjects": "298"}, {"groupId": "FG002", "numSubjects": "299"}, {"groupId": "FG003", "numSubjects": "285"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "15"}, {"groupId": "FG001", "numSubjects": "18"}, {"groupId": "FG002", "numSubjects": "22"}, {"groupId": "FG003", "numSubjects": "28"}]}], "dropWithdraws": [{"type": "Adverse Event", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "1"}, {"groupId": "FG002", "numSubjects": "5"}, {"groupId": "FG003", "numSubjects": "9"}]}, {"type": "Death", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}]}, {"type": "Lack of Efficacy", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "4"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "2"}, {"groupId": "FG002", "numSubjects": "2"}, {"groupId": "FG003", "numSubjects": "2"}]}, {"type": "Physician Decision", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "3"}, {"groupId": "FG002", "numSubjects": "2"}, {"groupId": "FG003", "numSubjects": "3"}]}, {"type": "Protocol Violation", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "0"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "1"}, {"groupId": "FG001", "numSubjects": "3"}, {"groupId": "FG002", "numSubjects": "2"}, {"groupId": "FG003", "numSubjects": "1"}]}, {"type": "Creatinine or eGFR withdrawal criteria", "reasons": [{"groupId": "FG000", "numSubjects": "2"}, {"groupId": "FG001", "numSubjects": "4"}, {"groupId": "FG002", "numSubjects": "3"}, {"groupId": "FG003", "numSubjects": "2"}]}, {"type": "Unable to take rescue therapy", "reasons": [{"groupId": "FG000", "numSubjects": "0"}, {"groupId": "FG001", "numSubjects": "0"}, {"groupId": "FG002", "numSubjects": "0"}, {"groupId": "FG003", "numSubjects": "1"}]}, {"type": "Other", "reasons": [{"groupId": "FG000", "numSubjects": "6"}, {"groupId": "FG001", "numSubjects": "5"}, {"groupId": "FG002", "numSubjects": "8"}, {"groupId": "FG003", "numSubjects": "5"}]}]}]}