{"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "Fountain Valley", "state": "California", "country": "United States", "geoPoint": {"lat": 33.70918, "lon": -117.95367}}, {"city": "National City", "state": "California", "country": "United States", "geoPoint": {"lat": 32.67811, "lon": -117.0992}}, {"city": "Northridge", "state": "California", "country": "United States", "geoPoint": {"lat": 34.22834, "lon": -118.53675}}, {"city": "Colorado Springs", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 38.83388, "lon": -104.82136}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.49893, "lon": -82.57482}}, {"city": "Brooksville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.55554, "lon": -82.38991}}, {"city": "Defuniak Springs", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.72102, "lon": -86.11522}}, {"city": "Hialeah", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.8576, "lon": -80.27811}}, {"city": "Niceville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.51686, "lon": -86.48217}}, {"city": "Tampa", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.94752, "lon": -82.45843}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Savannah", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.08354, "lon": -81.09983}}, {"city": "Champaign", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 40.11642, "lon": -88.24338}}, {"city": "Avon", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.76282, "lon": -86.39972}}, {"city": "<PERSON>s", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.95559, "lon": -86.01387}}, {"city": "<PERSON>", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 39.48061, "lon": -86.05499}}, {"city": "West Des Moines", "state": "Iowa", "country": "United States", "geoPoint": {"lat": 41.57721, "lon": -93.71133}}, {"city": "Wichita", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 37.69224, "lon": -97.33754}}, {"city": "Munfordville", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.27228, "lon": -85.89108}}, {"city": "Portland", "state": "Maine", "country": "United States", "geoPoint": {"lat": 43.66147, "lon": -70.25533}}, {"city": "Benzonia", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 44.62139, "lon": -86.09926}}, {"city": "Interlochen", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 44.64472, "lon": -85.7673}}, {"city": "Troy", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.60559, "lon": -83.14993}}, {"city": "Picayune", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 30.52556, "lon": -89.67788}}, {"city": "Florissant", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.78922, "lon": -90.32261}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Las Vegas", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.17497, "lon": -115.13722}}, {"city": "Mansfield", "state": "New Jersey", "country": "United States", "geoPoint": {"lat": 40.09122, "lon": -74.71377}}, {"city": "New Hyde Park", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.7351, "lon": -73.68791}}, {"city": "Asheboro", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.70791, "lon": -79.81364}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Kettering", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.6895, "lon": -84.16883}}, {"city": "Oregon City", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 45.35734, "lon": -122.60676}}, {"city": "Altoona", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.51868, "lon": -78.39474}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.6359, "lon": -78.29585}}, {"city": "East Providence", "state": "Rhode Island", "country": "United States", "geoPoint": {"lat": 41.81371, "lon": -71.37005}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "Mount Pleasant", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 32.79407, "lon": -79.86259}}, {"city": "Nashville", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.16589, "lon": -86.78444}}, {"city": "New Braunfels", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.703, "lon": -98.12445}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Bountiful", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.88939, "lon": -111.88077}}, {"city": "Spokane", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.65966, "lon": -117.42908}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Mendoza", "country": "Argentina", "geoPoint": {"lat": -32.89084, "lon": -68.82717}}, {"city": "San Juan", "country": "Argentina", "geoPoint": {"lat": -31.5375, "lon": -68.53639}}, {"city": "Pleven", "country": "Bulgaria", "geoPoint": {"lat": 43.41667, "lon": 24.61667}}, {"city": "Plovdiv", "country": "Bulgaria", "geoPoint": {"lat": 42.15, "lon": 24.75}}, {"city": "Sevlievo", "country": "Bulgaria", "geoPoint": {"lat": 43.02583, "lon": 25.11361}}, {"city": "Sofia", "country": "Bulgaria", "geoPoint": {"lat": 42.69751, "lon": 23.32415}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Colombia", "geoPoint": {"lat": 10.96854, "lon": -74.78132}}, {"city": "Bogota", "country": "Colombia", "geoPoint": {"lat": 4.60971, "lon": -74.08175}}, {"city": "Medellin", "country": "Colombia", "geoPoint": {"lat": 6.25184, "lon": -75.56359}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Czech Republic", "geoPoint": {"lat": 49.96382, "lon": 14.072}}, {"city": "Pardubice", "country": "Czech Republic", "geoPoint": {"lat": 50.04075, "lon": 15.77659}}, {"city": "Plzen", "country": "Czech Republic", "geoPoint": {"lat": 49.74747, "lon": 13.37759}}, {"city": "Rychnov Nad Kneznou", "country": "Czech Republic", "geoPoint": {"lat": 50.16284, "lon": 16.27488}}, {"city": "Tabor", "country": "Czech Republic", "geoPoint": {"lat": 49.41441, "lon": 14.6578}}, {"city": "Tallinn", "country": "Estonia", "geoPoint": {"lat": 59.43696, "lon": 24.75353}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Greece", "geoPoint": {"lat": 37.94745, "lon": 23.63708}}, {"city": "Thessalonikis", "country": "Greece"}, {"city": "Ahmedabad, Gujarat", "country": "India"}, {"city": "Aurangabad", "country": "India", "geoPoint": {"lat": 19.87757, "lon": 75.34226}}, {"city": "Bangalore, Karnataka", "country": "India"}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Belgaum", "country": "India", "geoPoint": {"lat": 15.85212, "lon": 74.50447}}, {"city": "Coimbatore", "country": "India", "geoPoint": {"lat": 11.00555, "lon": 76.96612}}, {"city": "Mumbai", "country": "India", "geoPoint": {"lat": 19.07283, "lon": 72.88261}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Trivandrum", "country": "India", "geoPoint": {"lat": 8.4855, "lon": 76.94924}}, {"city": "Daugavpils", "country": "Latvia", "geoPoint": {"lat": 55.88333, "lon": 26.53333}}, {"city": "Limbazi", "country": "Latvia", "geoPoint": {"lat": 57.51287, "lon": 24.71941}}, {"city": "Riga", "country": "Latvia", "geoPoint": {"lat": 56.946, "lon": 24.10589}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Latvia", "geoPoint": {"lat": 57.24562, "lon": 22.58137}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia"}, {"city": "Kuala Lumpur N/A", "country": "Malaysia"}, {"city": "Kuala Lumpur", "country": "Malaysia", "geoPoint": {"lat": 3.1412, "lon": 101.68653}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Malaysia", "geoPoint": {"lat": 3.55, "lon": 102.56667}}, {"city": "Selangor", "country": "Malaysia"}, {"city": "Culiacan", "country": "Mexico", "geoPoint": {"lat": 24.79032, "lon": -107.38782}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Que<PERSON>ro", "country": "Mexico"}, {"city": "Tampico", "country": "Mexico", "geoPoint": {"lat": 22.26695, "lon": -97.86815}}, {"city": "Lima 1 Lima Lima", "country": "Peru"}, {"city": "Leczyca", "country": "Poland", "geoPoint": {"lat": 52.05959, "lon": 19.19972}}, {"city": "Lodz", "country": "Poland", "geoPoint": {"lat": 51.75, "lon": 19.46667}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Zgierz", "country": "Poland", "geoPoint": {"lat": 51.85561, "lon": 19.40623}}, {"city": "Aveiro", "country": "Portugal", "geoPoint": {"lat": 40.64427, "lon": -8.64554}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Portugal", "geoPoint": {"lat": 39.74362, "lon": -8.80705}}, {"city": "Lisboa", "country": "Portugal", "geoPoint": {"lat": 38.71667, "lon": -9.13333}}, {"city": "Portalegre", "country": "Portugal", "geoPoint": {"lat": 39.29379, "lon": -7.43122}}, {"city": "San Juan", "country": "Puerto Rico", "geoPoint": {"lat": 18.46633, "lon": -66.10572}}, {"city": "Chelyabinsk", "country": "Russian Federation", "geoPoint": {"lat": 55.15402, "lon": 61.42915}}, {"city": "Dzerzhinsky Moscow Region", "country": "Russian Federation"}, {"city": "Ekaterinburg", "country": "Russian Federation", "geoPoint": {"lat": 56.8519, "lon": 60.6122}}, {"city": "Kemerovo", "country": "Russian Federation", "geoPoint": {"lat": 55.33333, "lon": 86.08333}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 53.20066, "lon": 45.00464}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "St-Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Singapore", "country": "Singapore", "geoPoint": {"lat": 1.28967, "lon": 103.85007}}, {"city": "Banska Bystrica", "country": "Slovakia", "geoPoint": {"lat": 48.73946, "lon": 19.15349}}, {"city": "Bratislava", "country": "Slovakia", "geoPoint": {"lat": 48.14816, "lon": 17.10674}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.71395, "lon": 21.25808}}, {"city": "<PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.07408, "lon": 18.94946}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 48.62858, "lon": 21.71954}}, {"city": "Göteborg", "country": "Sweden", "geoPoint": {"lat": 57.70716, "lon": 11.96679}}, {"city": "Stockholm", "country": "Sweden", "geoPoint": {"lat": 59.33258, "lon": 18.0649}}, {"city": "Bangkok", "country": "Thailand", "geoPoint": {"lat": 13.75398, "lon": 100.50144}}, {"city": "<PERSON>", "country": "Thailand", "geoPoint": {"lat": 18.79038, "lon": 98.98468}}, {"city": "<PERSON><PERSON>", "country": "Thailand", "geoPoint": {"lat": 16.44671, "lon": 102.833}}, {"city": "Ankara", "country": "Turkey", "geoPoint": {"lat": 39.91987, "lon": 32.85427}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Turkey", "geoPoint": {"lat": 36.90812, "lon": 30.69556}}, {"city": "Istanbul", "country": "Turkey", "geoPoint": {"lat": 41.01384, "lon": 28.94966}}, {"city": "Izmir", "country": "Turkey", "geoPoint": {"lat": 38.41273, "lon": 27.13838}}, {"city": "Konya", "country": "Turkey", "geoPoint": {"lat": 37.87135, "lon": 32.48464}}, {"city": "Donetsk", "country": "Ukraine", "geoPoint": {"lat": 48.023, "lon": 37.80224}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 48.9215, "lon": 24.70972}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "<PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 50.9216, "lon": 34.80029}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 49.84639, "lon": 37.71861}}, {"city": "Zaporozhye", "country": "Ukraine", "geoPoint": {"lat": 47.82289, "lon": 35.19031}}]}