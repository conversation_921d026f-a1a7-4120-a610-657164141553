{"groups": [{"id": "BG000", "title": "Placebo", "description": "Participants received matching placebo orally once daily."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Participants received canagliflozin 100 milligram (mg) orally once daily."}, {"id": "BG002", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "2199"}, {"groupId": "BG001", "value": "2202"}, {"groupId": "BG002", "value": "4401"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "63.2", "spread": "9.23"}, {"groupId": "BG001", "value": "62.9", "spread": "9.17"}, {"groupId": "BG002", "value": "63", "spread": "9.2"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "732"}, {"groupId": "BG001", "value": "762"}, {"groupId": "BG002", "value": "1494"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "1467"}, {"groupId": "BG001", "value": "1440"}, {"groupId": "BG002", "value": "2907"}]}]}]}, {"title": "Ethnicity (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "706"}, {"groupId": "BG001", "value": "717"}, {"groupId": "BG002", "value": "1423"}]}, {"title": "Not Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "1457"}, {"groupId": "BG001", "value": "1436"}, {"groupId": "BG002", "value": "2893"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "36"}, {"groupId": "BG001", "value": "49"}, {"groupId": "BG002", "value": "85"}]}]}]}, {"title": "Race/Ethnicity, Customized", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"title": "Asian", "categories": [{"measurements": [{"groupId": "BG000", "value": "452"}, {"groupId": "BG001", "value": "425"}, {"groupId": "BG002", "value": "877"}]}]}, {"title": "Black or African American", "categories": [{"measurements": [{"groupId": "BG000", "value": "112"}, {"groupId": "BG001", "value": "112"}, {"groupId": "BG002", "value": "224"}]}]}, {"title": "Hispanic or Latino", "categories": [{"measurements": [{"groupId": "BG000", "value": "554"}, {"groupId": "BG001", "value": "567"}, {"groupId": "BG002", "value": "1121"}]}]}, {"title": "Other", "categories": [{"measurements": [{"groupId": "BG000", "value": "199"}, {"groupId": "BG001", "value": "189"}, {"groupId": "BG002", "value": "388"}]}]}, {"title": "White Non-Hispanic", "categories": [{"measurements": [{"groupId": "BG000", "value": "882"}, {"groupId": "BG001", "value": "909"}, {"groupId": "BG002", "value": "1791"}]}]}]}, {"title": "Region of Enrollment", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"title": "ARGENTINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "205"}, {"groupId": "BG001", "value": "221"}, {"groupId": "BG002", "value": "426"}]}]}, {"title": "AUSTRALIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "18"}, {"groupId": "BG001", "value": "20"}, {"groupId": "BG002", "value": "38"}]}]}, {"title": "BRAZIL", "categories": [{"measurements": [{"groupId": "BG000", "value": "162"}, {"groupId": "BG001", "value": "152"}, {"groupId": "BG002", "value": "314"}]}]}, {"title": "BULGARIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "13"}, {"groupId": "BG001", "value": "16"}, {"groupId": "BG002", "value": "29"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "93"}, {"groupId": "BG001", "value": "79"}, {"groupId": "BG002", "value": "172"}]}]}, {"title": "CHILE", "categories": [{"measurements": [{"groupId": "BG000", "value": "30"}, {"groupId": "BG001", "value": "22"}, {"groupId": "BG002", "value": "52"}]}]}, {"title": "CHINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "63"}, {"groupId": "BG001", "value": "66"}, {"groupId": "BG002", "value": "129"}]}]}, {"title": "COLOMBIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "42"}, {"groupId": "BG001", "value": "52"}, {"groupId": "BG002", "value": "94"}]}]}, {"title": "CZECH REPUBLIC", "categories": [{"measurements": [{"groupId": "BG000", "value": "26"}, {"groupId": "BG001", "value": "31"}, {"groupId": "BG002", "value": "57"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "33"}, {"groupId": "BG001", "value": "28"}, {"groupId": "BG002", "value": "61"}]}]}, {"title": "GERMANY", "categories": [{"measurements": [{"groupId": "BG000", "value": "6"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "11"}]}]}, {"title": "GUATEMALA", "categories": [{"measurements": [{"groupId": "BG000", "value": "26"}, {"groupId": "BG001", "value": "29"}, {"groupId": "BG002", "value": "55"}]}]}, {"title": "HUNGARY", "categories": [{"measurements": [{"groupId": "BG000", "value": "68"}, {"groupId": "BG001", "value": "67"}, {"groupId": "BG002", "value": "135"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "82"}, {"groupId": "BG001", "value": "62"}, {"groupId": "BG002", "value": "144"}]}]}, {"title": "ITALY", "categories": [{"measurements": [{"groupId": "BG000", "value": "39"}, {"groupId": "BG001", "value": "51"}, {"groupId": "BG002", "value": "90"}]}]}, {"title": "JAPAN", "categories": [{"measurements": [{"groupId": "BG000", "value": "53"}, {"groupId": "BG001", "value": "57"}, {"groupId": "BG002", "value": "110"}]}]}, {"title": "LITHUANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "4"}, {"groupId": "BG002", "value": "7"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "72"}, {"groupId": "BG001", "value": "63"}, {"groupId": "BG002", "value": "135"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "152"}, {"groupId": "BG001", "value": "151"}, {"groupId": "BG002", "value": "303"}]}]}, {"title": "NEW ZEALAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "30"}, {"groupId": "BG001", "value": "31"}, {"groupId": "BG002", "value": "61"}]}]}, {"title": "PHILIPPINES", "categories": [{"measurements": [{"groupId": "BG000", "value": "35"}, {"groupId": "BG001", "value": "36"}, {"groupId": "BG002", "value": "71"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "27"}, {"groupId": "BG001", "value": "23"}, {"groupId": "BG002", "value": "50"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "27"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "59"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "76"}, {"groupId": "BG001", "value": "57"}, {"groupId": "BG002", "value": "133"}]}]}, {"title": "SERBIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "19"}, {"groupId": "BG001", "value": "21"}, {"groupId": "BG002", "value": "40"}]}]}, {"title": "SLOVAKIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "32"}, {"groupId": "BG001", "value": "34"}, {"groupId": "BG002", "value": "66"}]}]}, {"title": "SOUTH AFRICA", "categories": [{"measurements": [{"groupId": "BG000", "value": "34"}, {"groupId": "BG001", "value": "28"}, {"groupId": "BG002", "value": "62"}]}]}, {"title": "SOUTH KOREA", "categories": [{"measurements": [{"groupId": "BG000", "value": "58"}, {"groupId": "BG001", "value": "64"}, {"groupId": "BG002", "value": "122"}]}]}, {"title": "SPAIN", "categories": [{"measurements": [{"groupId": "BG000", "value": "58"}, {"groupId": "BG001", "value": "83"}, {"groupId": "BG002", "value": "141"}]}]}, {"title": "TAIWAN", "categories": [{"measurements": [{"groupId": "BG000", "value": "22"}, {"groupId": "BG001", "value": "15"}, {"groupId": "BG002", "value": "37"}]}]}, {"title": "UKRAINE", "categories": [{"measurements": [{"groupId": "BG000", "value": "173"}, {"groupId": "BG001", "value": "198"}, {"groupId": "BG002", "value": "371"}]}]}, {"title": "UNITED ARAB EMIRATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "1"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "59"}, {"groupId": "BG001", "value": "59"}, {"groupId": "BG002", "value": "118"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "363"}, {"groupId": "BG001", "value": "344"}, {"groupId": "BG002", "value": "707"}]}]}]}]}