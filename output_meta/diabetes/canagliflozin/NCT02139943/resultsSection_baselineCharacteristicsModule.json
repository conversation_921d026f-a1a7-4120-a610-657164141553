{"populationDescription": "Modified intent-to-treat analysis set included all randomized participants who took at least 1 dose of double-blind study drug.", "groups": [{"id": "BG000", "title": "Placebo", "description": "Participants received canagliflozin matching placebo capsules once daily for 18 weeks."}, {"id": "BG001", "title": "Canagliflozin 100 Milligram (mg)", "description": "Participants received 100 mg of canagliflozin capsules once daily for 18 weeks."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Participants received 300 mg of canagliflozin capsules once daily for 18 weeks."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "117"}, {"groupId": "BG001", "value": "117"}, {"groupId": "BG002", "value": "117"}, {"groupId": "BG003", "value": "351"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "117"}, {"groupId": "BG001", "value": "115"}, {"groupId": "BG002", "value": "115"}, {"groupId": "BG003", "value": "347"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "4"}]}]}]}, {"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "42", "spread": "11.9"}, {"groupId": "BG001", "value": "42", "spread": "11.6"}, {"groupId": "BG002", "value": "42.8", "spread": "10.96"}, {"groupId": "BG003", "value": "42.3", "spread": "11.47"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "54"}, {"groupId": "BG001", "value": "48"}, {"groupId": "BG002", "value": "52"}, {"groupId": "BG003", "value": "154"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "63"}, {"groupId": "BG001", "value": "69"}, {"groupId": "BG002", "value": "65"}, {"groupId": "BG003", "value": "197"}]}]}]}, {"title": "Region Enroll", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "Canada", "categories": [{"measurements": [{"groupId": "BG000", "value": "24"}, {"groupId": "BG001", "value": "19"}, {"groupId": "BG002", "value": "20"}, {"groupId": "BG003", "value": "63"}]}]}, {"title": "United Stats", "categories": [{"measurements": [{"groupId": "BG000", "value": "93"}, {"groupId": "BG001", "value": "98"}, {"groupId": "BG002", "value": "97"}, {"groupId": "BG003", "value": "288"}]}]}]}]}