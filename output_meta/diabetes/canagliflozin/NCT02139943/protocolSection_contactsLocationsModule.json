{"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "Concord", "state": "California", "country": "United States", "geoPoint": {"lat": 37.97798, "lon": -122.03107}}, {"city": "La Jolla", "state": "California", "country": "United States", "geoPoint": {"lat": 32.84727, "lon": -117.2742}}, {"city": "Los Angeles", "state": "California", "country": "United States", "geoPoint": {"lat": 34.05223, "lon": -118.24368}}, {"city": "Los Gatos", "state": "California", "country": "United States", "geoPoint": {"lat": 37.22661, "lon": -121.97468}}, {"city": "Northridge", "state": "California", "country": "United States", "geoPoint": {"lat": 34.22834, "lon": -118.53675}}, {"city": "Orange", "state": "California", "country": "United States", "geoPoint": {"lat": 33.78779, "lon": -117.85311}}, {"city": "San Francisco", "state": "California", "country": "United States", "geoPoint": {"lat": 37.77493, "lon": -122.41942}}, {"city": "Teme<PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.49364, "lon": -117.14836}}, {"city": "<PERSON><PERSON><PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 33.74585, "lon": -117.82617}}, {"city": "Ventura", "state": "California", "country": "United States", "geoPoint": {"lat": 34.27834, "lon": -119.29317}}, {"city": "Walnut Creek", "state": "California", "country": "United States", "geoPoint": {"lat": 37.90631, "lon": -122.06496}}, {"city": "Denver", "state": "Colorado", "country": "United States", "geoPoint": {"lat": 39.73915, "lon": -104.9847}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "Palm Harbor", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.07807, "lon": -82.76371}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Honolulu", "state": "Hawaii", "country": "United States", "geoPoint": {"lat": 21.30694, "lon": -157.85833}}, {"city": "Council Bluffs", "state": "Iowa", "country": "United States", "geoPoint": {"lat": 41.26194, "lon": -95.86083}}, {"city": "Des Moines", "state": "Iowa", "country": "United States", "geoPoint": {"lat": 41.60054, "lon": -93.60911}}, {"city": "Baton Rouge", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 30.45075, "lon": -91.15455}}, {"city": "Rockville", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.084, "lon": -77.15276}}, {"city": "<PERSON><PERSON>", "state": "Montana", "country": "United States", "geoPoint": {"lat": 45.78329, "lon": -108.50069}}, {"city": "Omaha", "state": "Nebraska", "country": "United States", "geoPoint": {"lat": 41.25626, "lon": -95.94043}}, {"city": "El Paso", "state": "Nevada", "country": "United States"}, {"city": "Las Vegas", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.17497, "lon": -115.13722}}, {"city": "Nashua", "state": "New Hampshire", "country": "United States", "geoPoint": {"lat": 42.76537, "lon": -71.46757}}, {"city": "<PERSON><PERSON>", "state": "New York", "country": "United States", "geoPoint": {"lat": 41.6712, "lon": -73.76319}}, {"city": "Smithtown", "state": "New York", "country": "United States", "geoPoint": {"lat": 40.85593, "lon": -73.20067}}, {"city": "Morehead City", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.72294, "lon": -76.72604}}, {"city": "Columbus", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.96118, "lon": -82.99879}}, {"city": "Mentor", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.66616, "lon": -81.33955}}, {"city": "Philadelphia", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 39.95233, "lon": -75.16379}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "Bloomington", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.58034, "lon": -85.19274}}, {"city": "Arlington", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.73569, "lon": -97.10807}}, {"city": "Austin", "state": "Texas", "country": "United States", "geoPoint": {"lat": 30.26715, "lon": -97.74306}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "El Paso", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.75872, "lon": -106.48693}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.55217, "lon": -98.26973}}, {"city": "<PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 30.09716, "lon": -95.61605}}, {"city": "<PERSON>", "state": "Utah", "country": "United States", "geoPoint": {"lat": 41.223, "lon": -111.97383}}, {"city": "Salt Lake City", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.76078, "lon": -111.89105}}, {"city": "Spokane", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.65966, "lon": -117.42908}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "Calgary", "state": "Alberta", "country": "Canada", "geoPoint": {"lat": 51.05011, "lon": -114.08529}}, {"city": "London", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.98339, "lon": -81.23304}}, {"city": "Oakville", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.45011, "lon": -79.68292}}, {"city": "Thornhill", "state": "Ontario", "country": "Canada"}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "<PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.56995, "lon": -73.692}}, {"city": "Sainte-<PERSON><PERSON>", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.75615, "lon": -71.29543}}]}