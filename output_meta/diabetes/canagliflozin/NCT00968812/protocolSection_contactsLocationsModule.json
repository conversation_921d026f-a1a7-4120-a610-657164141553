{"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Calera", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 33.1029, "lon": -86.7536}}, {"city": "<PERSON>", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.35283, "lon": -111.78903}}, {"city": "Mesa", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.42227, "lon": -111.82264}}, {"city": "Tucson", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 32.22174, "lon": -110.92648}}, {"city": "Jonesboro", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 35.8423, "lon": -90.70428}}, {"city": "Buena Park", "state": "California", "country": "United States", "geoPoint": {"lat": 33.86751, "lon": -117.99812}}, {"city": "Encinitas", "state": "California", "country": "United States", "geoPoint": {"lat": 33.03699, "lon": -117.29198}}, {"city": "Fresno", "state": "California", "country": "United States", "geoPoint": {"lat": 36.74773, "lon": -119.77237}}, {"city": "Lincoln", "state": "California", "country": "United States", "geoPoint": {"lat": 38.89156, "lon": -121.29301}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "San Diego", "state": "California", "country": "United States", "geoPoint": {"lat": 32.71533, "lon": -117.15726}}, {"city": "Westlake Village", "state": "California", "country": "United States", "geoPoint": {"lat": 34.14584, "lon": -118.80565}}, {"city": "<PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.78186, "lon": -85.53854}}, {"city": "<PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.77436, "lon": -85.22687}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.03418, "lon": -82.6651}}, {"city": "Orlando", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.53834, "lon": -81.37924}}, {"city": "Augusta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.47097, "lon": -81.97484}}, {"city": "<PERSON>", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 32.45821, "lon": -83.73157}}, {"city": "<PERSON><PERSON>", "state": "Idaho", "country": "United States", "geoPoint": {"lat": 43.54072, "lon": -116.56346}}, {"city": "Chicago", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 41.85003, "lon": -87.65005}}, {"city": "<PERSON>", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 42.21947, "lon": -87.97952}}, {"city": "Valparaiso", "state": "Indiana", "country": "United States", "geoPoint": {"lat": 41.47309, "lon": -87.06114}}, {"city": "New Orleans", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.95465, "lon": -90.07507}}, {"city": "Elkridge", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.21261, "lon": -76.71358}}, {"city": "Bloomfield Hills", "state": "Michigan", "country": "United States", "geoPoint": {"lat": 42.58364, "lon": -83.24549}}, {"city": "Las Vegas", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.17497, "lon": -115.13722}}, {"city": "Canal Fulton", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 40.88978, "lon": -81.59762}}, {"city": "Gallipolis", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 38.8098, "lon": -82.20237}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.36006, "lon": -84.30994}}, {"city": "Perrysburg", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 41.557, "lon": -83.62716}}, {"city": "Tulsa", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 36.15398, "lon": -95.99277}}, {"city": "Yukon", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 35.50672, "lon": -97.76254}}, {"city": "Fleetwood", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.45398, "lon": -75.81798}}, {"city": "Greenville", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.85262, "lon": -82.39401}}, {"city": "Nashville", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.16589, "lon": -86.78444}}, {"city": "Houston", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.76328, "lon": -95.36327}}, {"city": "Odessa", "state": "Texas", "country": "United States", "geoPoint": {"lat": 31.84568, "lon": -102.36764}}, {"city": "Pearland", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.56357, "lon": -95.28605}}, {"city": "San Antonio", "state": "Texas", "country": "United States", "geoPoint": {"lat": 29.42412, "lon": -98.49363}}, {"city": "Danville", "state": "Virginia", "country": "United States", "geoPoint": {"lat": 36.58597, "lon": -79.39502}}, {"city": "Olympia", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.03787, "lon": -122.9007}}, {"city": "Milwaukee", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.0389, "lon": -87.90647}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "state": "Wisconsin", "country": "United States", "geoPoint": {"lat": 43.04946, "lon": -88.00759}}, {"city": "Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Ciudad Autonoma De Buenos Aires", "country": "Argentina", "geoPoint": {"lat": -34.61315, "lon": -58.37723}}, {"city": "Mar Del Plata", "country": "Argentina", "geoPoint": {"lat": -38.00228, "lon": -57.55754}}, {"city": "Rosario", "country": "Argentina", "geoPoint": {"lat": -32.94682, "lon": -60.63932}}, {"city": "Dimitrovgrad", "country": "Bulgaria", "geoPoint": {"lat": 42.05, "lon": 25.6}}, {"city": "<PERSON><PERSON>", "country": "Bulgaria", "geoPoint": {"lat": 42.61667, "lon": 25.4}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Bulgaria", "geoPoint": {"lat": 43.85639, "lon": 25.97083}}, {"city": "Sofia", "country": "Bulgaria", "geoPoint": {"lat": 42.69751, "lon": 23.32415}}, {"city": "Chilliwack", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.16638, "lon": -121.95257}}, {"city": "<PERSON><PERSON><PERSON>", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.88307, "lon": -119.48568}}, {"city": "Vancouver", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.24966, "lon": -123.11934}}, {"city": "St. John'S", "state": "Newfoundland and Labrador", "country": "Canada", "geoPoint": {"lat": 47.56494, "lon": -52.70931}}, {"city": "Mississauga", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.5789, "lon": -79.6583}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Saskatoon", "state": "Saskatchewan", "country": "Canada", "geoPoint": {"lat": 52.13238, "lon": -106.66892}}, {"city": "Quebec", "country": "Canada", "geoPoint": {"lat": 46.81228, "lon": -71.21454}}, {"city": "San Jose", "country": "Costa Rica", "geoPoint": {"lat": 9.93333, "lon": -84.08333}}, {"city": "San Pedro", "country": "Costa Rica", "geoPoint": {"lat": 9.92829, "lon": -84.05074}}, {"city": "Aalborg", "country": "Denmark", "geoPoint": {"lat": 57.048, "lon": 9.9187}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Denmark", "geoPoint": {"lat": 55.73165, "lon": 12.36328}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Denmark", "geoPoint": {"lat": 55.70927, "lon": 9.5357}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Denmark"}, {"city": "Helsinki", "country": "Finland", "geoPoint": {"lat": 60.16952, "lon": 24.93545}}, {"city": "Kokkola", "country": "Finland", "geoPoint": {"lat": 63.83847, "lon": 23.13066}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Finland", "geoPoint": {"lat": 62.89238, "lon": 27.67703}}, {"city": "Oulu", "country": "Finland", "geoPoint": {"lat": 65.01236, "lon": 25.46816}}, {"city": "Turku", "country": "Finland", "geoPoint": {"lat": 60.45148, "lon": 22.26869}}, {"city": "Berlin", "country": "Germany", "geoPoint": {"lat": 52.52437, "lon": 13.41053}}, {"city": "Dresden", "country": "Germany", "geoPoint": {"lat": 51.05089, "lon": 13.73832}}, {"city": "Duesseldorf", "country": "Germany", "geoPoint": {"lat": 51.22172, "lon": 6.77616}}, {"city": "Hamburg", "country": "Germany", "geoPoint": {"lat": 53.57532, "lon": 10.01534}}, {"city": "Mainz", "country": "Germany", "geoPoint": {"lat": 49.98419, "lon": 8.2791}}, {"city": "Villingen-Schwenningen", "country": "Germany", "geoPoint": {"lat": 48.06226, "lon": 8.49358}}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Chennai", "country": "India", "geoPoint": {"lat": 13.08784, "lon": 80.27847}}, {"city": "Coimbatore", "country": "India", "geoPoint": {"lat": 11.00555, "lon": 76.96612}}, {"city": "Hyderabad", "country": "India", "geoPoint": {"lat": 17.38405, "lon": 78.45636}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "<PERSON><PERSON>", "country": "India", "geoPoint": {"lat": 20.73933, "lon": 78.59784}}, {"city": "Beer Sheba", "country": "Israel", "geoPoint": {"lat": 31.25181, "lon": 34.7913}}, {"city": "Haifa", "country": "Israel", "geoPoint": {"lat": 32.81841, "lon": 34.9885}}, {"city": "<PERSON>lon", "country": "Israel", "geoPoint": {"lat": 32.01034, "lon": 34.77918}}, {"city": "Jerusalem", "country": "Israel", "geoPoint": {"lat": 31.76904, "lon": 35.21633}}, {"city": "<PERSON><PERSON>", "country": "Israel", "geoPoint": {"lat": 32.08227, "lon": 34.81065}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Israel", "geoPoint": {"lat": 31.89421, "lon": 34.81199}}, {"city": "Tel Aviv", "country": "Israel", "geoPoint": {"lat": 32.08088, "lon": 34.78057}}, {"city": "Tel-Aviv", "country": "Israel", "geoPoint": {"lat": 32.08088, "lon": 34.78057}}, {"city": "Zefat", "country": "Israel", "geoPoint": {"lat": 32.96465, "lon": 35.496}}, {"city": "Daegu", "country": "Korea, Republic of", "geoPoint": {"lat": 35.87028, "lon": 128.59111}}, {"city": "Goyang-Si", "country": "Korea, Republic of", "geoPoint": {"lat": 37.65639, "lon": 126.835}}, {"city": "Gyeonggi-Do", "country": "Korea, Republic of", "geoPoint": {"lat": 37.58944, "lon": 126.76917}}, {"city": "Incheon", "country": "Korea, Republic of", "geoPoint": {"lat": 37.45646, "lon": 126.70515}}, {"city": "Seoul", "country": "Korea, Republic of", "geoPoint": {"lat": 37.566, "lon": 126.9784}}, {"city": "<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.29111, "lon": 127.00889}}, {"city": "Wonju<PERSON><PERSON>", "country": "Korea, Republic of"}, {"city": "<PERSON><PERSON>", "country": "Korea, Republic of", "geoPoint": {"lat": 37.35139, "lon": 127.94528}}, {"city": "Ciudad De Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Mexico", "country": "Mexico", "geoPoint": {"lat": 19.42847, "lon": -99.12766}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Alesund", "country": "Norway", "geoPoint": {"lat": 62.47225, "lon": 6.15492}}, {"city": "Oslo", "country": "Norway", "geoPoint": {"lat": 59.91273, "lon": 10.74609}}, {"city": "Cebu", "country": "Philippines", "geoPoint": {"lat": 10.31672, "lon": 123.89071}}, {"city": "Marikina City", "country": "Philippines", "geoPoint": {"lat": 14.6481, "lon": 121.1133}}, {"city": "<PERSON><PERSON>", "country": "Philippines", "geoPoint": {"lat": 14.53748, "lon": 121.00144}}, {"city": "Quezon City", "country": "Philippines", "geoPoint": {"lat": 14.6488, "lon": 121.0509}}, {"city": "Bydgoszcz", "country": "Poland", "geoPoint": {"lat": 53.1235, "lon": 18.00762}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "Kutno 001", "country": "Poland", "geoPoint": {"lat": 52.23064, "lon": 19.36409}}, {"city": "Lodz", "country": "Poland", "geoPoint": {"lat": 51.75, "lon": 19.46667}}, {"city": "Lublin", "country": "Poland", "geoPoint": {"lat": 51.25, "lon": 22.56667}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Zielona Gora", "country": "Poland", "geoPoint": {"lat": 51.93548, "lon": 15.50643}}, {"city": "Ponce", "country": "Puerto Rico", "geoPoint": {"lat": 18.01108, "lon": -66.61406}}, {"city": "Baia Mare", "country": "Romania", "geoPoint": {"lat": 47.65529, "lon": 23.57381}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.64861, "lon": 25.60613}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.76667, "lon": 23.6}}, {"city": "<PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 45.45, "lon": 28.05}}, {"city": "P<PERSON>iesti", "country": "Romania", "geoPoint": {"lat": 44.95, "lon": 26.01667}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Romania", "geoPoint": {"lat": 46.54245, "lon": 24.55747}}, {"city": "Arkhangelsk", "country": "Russian Federation", "geoPoint": {"lat": 64.5401, "lon": 40.5433}}, {"city": "Moscow", "country": "Russian Federation", "geoPoint": {"lat": 55.75222, "lon": 37.61556}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "<PERSON><PERSON>", "country": "Russian Federation", "geoPoint": {"lat": 51.54056, "lon": 46.00861}}, {"city": "St Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Banska Bystrica", "country": "Slovakia", "geoPoint": {"lat": 48.73946, "lon": 19.15349}}, {"city": "Bratislava", "country": "Slovakia", "geoPoint": {"lat": 48.14816, "lon": 17.10674}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Slovakia", "geoPoint": {"lat": 49.12011, "lon": 19.16891}}, {"city": "Presov", "country": "Slovakia", "geoPoint": {"lat": 48.99839, "lon": 21.23393}}, {"city": "Dnepropetrovsk", "country": "Ukraine", "geoPoint": {"lat": 48.4593, "lon": 35.03864}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Poltava", "country": "Ukraine", "geoPoint": {"lat": 49.59373, "lon": 34.54073}}, {"city": "Ternopil", "country": "Ukraine", "geoPoint": {"lat": 49.55589, "lon": 25.60556}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Ukraine", "geoPoint": {"lat": 49.84639, "lon": 37.71861}}]}