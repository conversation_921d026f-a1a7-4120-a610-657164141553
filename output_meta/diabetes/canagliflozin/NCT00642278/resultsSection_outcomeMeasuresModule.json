{"outcomeMeasures": [{"type": "PRIMARY", "title": "Change in HbA1c From Baseline to Week 12", "description": "The table below shows the mean change in HbA1c from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "Percent", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "61"}, {"groupId": "OG001", "value": "62"}, {"groupId": "OG002", "value": "62"}, {"groupId": "OG003", "value": "62"}, {"groupId": "OG004", "value": "60"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "62"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-0.22", "spread": "0.702"}, {"groupId": "OG001", "value": "-0.79", "spread": "0.749"}, {"groupId": "OG002", "value": "-0.76", "spread": "0.992"}, {"groupId": "OG003", "value": "-0.70", "spread": "0.720"}, {"groupId": "OG004", "value": "-0.92", "spread": "0.695"}, {"groupId": "OG005", "value": "-0.95", "spread": "0.704"}, {"groupId": "OG006", "value": "-0.74", "spread": "0.615"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.45", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.747", "ciUpperLimit": "-0.148", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.51", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.804", "ciUpperLimit": "-0.207", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.54", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.841", "ciUpperLimit": "-0.244", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.71", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.006", "ciUpperLimit": "-0.405", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.117"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.73", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.029", "ciUpperLimit": "-0.432", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "pValueComment": "Adjusted using <PERSON><PERSON>'s procedure.", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.56", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.862", "ciUpperLimit": "-0.265", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.116"}]}, {"type": "SECONDARY", "title": "Change in Fasting Plasma Glucose (FPG) From Baseline to Week 12", "description": "The table below shows the mean change in FPG from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "mmol/L", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "62"}, {"groupId": "OG001", "value": "63"}, {"groupId": "OG002", "value": "63"}, {"groupId": "OG003", "value": "62"}, {"groupId": "OG004", "value": "61"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "64"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "0.2", "spread": "1.58"}, {"groupId": "OG001", "value": "-0.9", "spread": "2.26"}, {"groupId": "OG002", "value": "-1.4", "spread": "1.70"}, {"groupId": "OG003", "value": "-1.5", "spread": "2.23"}, {"groupId": "OG004", "value": "-1.4", "spread": "1.87"}, {"groupId": "OG005", "value": "-1.3", "spread": "1.54"}, {"groupId": "OG006", "value": "-0.7", "spread": "1.77"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-0.9", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.39", "ciUpperLimit": "-0.34", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.4", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.98", "ciUpperLimit": "-0.92", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.8", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.33", "ciUpperLimit": "-1.27", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.8", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.32", "ciUpperLimit": "-1.26", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.7", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.25", "ciUpperLimit": "-1.19", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-1.0", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-1.51", "ciUpperLimit": "-0.46", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.27"}]}, {"type": "SECONDARY", "title": "Percentage of Patients With Symptoms of Hypoglycemia", "description": "The table below shows the percentage of patients who experienced symptomatic hypoglycemic events between Baseline and Week 12.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomiy assigned to a treatment group.", "reportingStatus": "POSTED", "paramType": "NUMBER", "unitOfMeasure": "Percentage of patients", "timeFrame": "Up to Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "65"}, {"groupId": "OG001", "value": "64"}, {"groupId": "OG002", "value": "64"}, {"groupId": "OG003", "value": "65"}, {"groupId": "OG004", "value": "64"}, {"groupId": "OG005", "value": "64"}, {"groupId": "OG006", "value": "65"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "2"}, {"groupId": "OG001", "value": "0"}, {"groupId": "OG002", "value": "2"}, {"groupId": "OG003", "value": "6"}, {"groupId": "OG004", "value": "0"}, {"groupId": "OG005", "value": "3"}, {"groupId": "OG006", "value": "5"}]}]}]}, {"type": "SECONDARY", "title": "Change in Overnight Urine Glucose/Creatinine Ratio From Baseline to Week 12", "description": "The table below shows the mean change in overnight urine glucose/creatinine ratio from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "mg/mg", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "54"}, {"groupId": "OG001", "value": "58"}, {"groupId": "OG002", "value": "56"}, {"groupId": "OG003", "value": "53"}, {"groupId": "OG004", "value": "57"}, {"groupId": "OG005", "value": "56"}, {"groupId": "OG006", "value": "58"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "1.9", "spread": "12.34"}, {"groupId": "OG001", "value": "35.4", "spread": "28.98"}, {"groupId": "OG002", "value": "51.5", "spread": "28.83"}, {"groupId": "OG003", "value": "50.5", "spread": "24.38"}, {"groupId": "OG004", "value": "49.4", "spread": "38.41"}, {"groupId": "OG005", "value": "61.6", "spread": "37.85"}, {"groupId": "OG006", "value": "-1.9", "spread": "14.78"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "36.1", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "26.07", "ciUpperLimit": "46.13", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.10"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "49.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "39.17", "ciUpperLimit": "59.34", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.13"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "48.2", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "37.98", "ciUpperLimit": "58.42", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.2"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "49.0", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "38.91", "ciUpperLimit": "59.01", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.11"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "60.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "50.17", "ciUpperLimit": "70.35", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.13"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.513", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Squares Mean Difference", "paramValue": "-3.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-13.33", "ciUpperLimit": "6.67", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "5.09"}]}, {"type": "SECONDARY", "title": "Absolute Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean absolute change in body weight from Baseline to Week 12 for each treatment group.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "kg", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "62"}, {"groupId": "OG001", "value": "63"}, {"groupId": "OG002", "value": "64"}, {"groupId": "OG003", "value": "63"}, {"groupId": "OG004", "value": "62"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "64"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-0.78", "spread": "2.099"}, {"groupId": "OG001", "value": "-1.96", "spread": "2.334"}, {"groupId": "OG002", "value": "-2.25", "spread": "2.145"}, {"groupId": "OG003", "value": "-2.32", "spread": "2.842"}, {"groupId": "OG004", "value": "-2.88", "spread": "2.391"}, {"groupId": "OG005", "value": "-2.87", "spread": "2.344"}, {"groupId": "OG006", "value": "-0.43", "spread": "2.693"}]}]}]}, {"type": "SECONDARY", "title": "Percent Change in Body Weight From Baseline to Week 12", "description": "The table below shows the mean percent change in body weight from Baseline to Week 12 for each treatment group. The statistical analyses show the treatment differences (ie, each canagliflozin or sitagliptin group minus placebo) in the least-squares mean change.", "populationDescription": "This analysis was conducted using the intent-to-treat analysis set, which included all patients who were randomly assigned to a treatment group. The last-observation-carried-forward method was applied when Week 12 values were missing. The table includes only patients with both baseline and post baseline values.", "reportingStatus": "POSTED", "paramType": "MEAN", "dispersionType": "Standard Deviation", "unitOfMeasure": "Percent change", "timeFrame": "Day 1 (Baseline) and Week 12", "groups": [{"id": "OG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "OG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "OG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "OG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}], "denoms": [{"units": "Participants", "counts": [{"groupId": "OG000", "value": "62"}, {"groupId": "OG001", "value": "63"}, {"groupId": "OG002", "value": "64"}, {"groupId": "OG003", "value": "63"}, {"groupId": "OG004", "value": "62"}, {"groupId": "OG005", "value": "62"}, {"groupId": "OG006", "value": "64"}]}], "classes": [{"categories": [{"measurements": [{"groupId": "OG000", "value": "-1.1", "spread": "2.4"}, {"groupId": "OG001", "value": "-2.3", "spread": "2.8"}, {"groupId": "OG002", "value": "-2.6", "spread": "2.3"}, {"groupId": "OG003", "value": "-2.7", "spread": "3.0"}, {"groupId": "OG004", "value": "-3.4", "spread": "2.8"}, {"groupId": "OG005", "value": "-3.4", "spread": "2.6"}, {"groupId": "OG006", "value": "-0.6", "spread": "3.0"}]}]}], "analyses": [{"groupIds": ["OG000", "OG001"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.009", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-1.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.2", "ciUpperLimit": "-0.3", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG002"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.002", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-1.5", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.5", "ciUpperLimit": "-0.6", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG003"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and mixed meal tolerance test.", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-1.6", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-2.6", "ciUpperLimit": "-0.7", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG004"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-2.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.3", "ciUpperLimit": "-1.4", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG005"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "<0.001", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "-2.3", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-3.3", "ciUpperLimit": "-1.4", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}, {"groupIds": ["OG000", "OG006"], "testedNonInferiority": false, "nonInferiorityType": "SUPERIORITY_OR_OTHER", "pValue": "0.371", "statisticalMethod": "ANCOVA", "statisticalComment": "ANCOVA model included terms for treatment, baseline value, and stratification factor (participation in mixed meal tolerance test).", "paramType": "Least-Sqaures Mean Difference", "paramValue": "0.4", "ciPctValue": "95", "ciNumSides": "TWO_SIDED", "ciLowerLimit": "-0.5", "ciUpperLimit": "1.4", "dispersionType": "STANDARD_ERROR_OF_MEAN", "dispersionValue": "0.5"}]}]}