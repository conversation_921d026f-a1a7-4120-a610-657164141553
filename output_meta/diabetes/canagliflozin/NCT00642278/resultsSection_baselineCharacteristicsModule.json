{"groups": [{"id": "BG000", "title": "Placebo", "description": "Each patient received matching placebo twice daily for 12 weeks."}, {"id": "BG001", "title": "Canagliflozin 50 mg Daily", "description": "Each patient received 50 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG002", "title": "Canagliflozin 100 mg Daily", "description": "Each patient received 100 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG003", "title": "Canagliflozin 200 mg Daily", "description": "Each patient received 200 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG004", "title": "Canagliflozin 300 mg Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG005", "title": "Canagliflozin 300 mg Twice Daily", "description": "Each patient received 300 mg of canagliflozin (JNJ-28431754) twice daily for 12 weeks."}, {"id": "BG006", "title": "Sitagliptin 100 mg Daily", "description": "Each patient received 100 mg of sitagliptin once daily (in the morning) for 12 weeks with matching placebo once daily (in the evening)."}, {"id": "BG007", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "65"}, {"groupId": "BG001", "value": "64"}, {"groupId": "BG002", "value": "64"}, {"groupId": "BG003", "value": "65"}, {"groupId": "BG004", "value": "64"}, {"groupId": "BG005", "value": "64"}, {"groupId": "BG006", "value": "65"}, {"groupId": "BG007", "value": "451"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}, {"groupId": "BG004", "value": "0"}, {"groupId": "BG005", "value": "0"}, {"groupId": "BG006", "value": "0"}, {"groupId": "BG007", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "63"}, {"groupId": "BG001", "value": "61"}, {"groupId": "BG002", "value": "63"}, {"groupId": "BG003", "value": "61"}, {"groupId": "BG004", "value": "63"}, {"groupId": "BG005", "value": "62"}, {"groupId": "BG006", "value": "65"}, {"groupId": "BG007", "value": "438"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "4"}, {"groupId": "BG004", "value": "1"}, {"groupId": "BG005", "value": "2"}, {"groupId": "BG006", "value": "0"}, {"groupId": "BG007", "value": "13"}]}]}]}, {"title": "Age Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "53.3", "spread": "7.82"}, {"groupId": "BG001", "value": "53.3", "spread": "8.48"}, {"groupId": "BG002", "value": "51.7", "spread": "7.95"}, {"groupId": "BG003", "value": "52.9", "spread": "9.56"}, {"groupId": "BG004", "value": "52.3", "spread": "6.88"}, {"groupId": "BG005", "value": "55.2", "spread": "7.14"}, {"groupId": "BG006", "value": "51.7", "spread": "8.09"}, {"groupId": "BG007", "value": "52.9", "spread": "8.06"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "34"}, {"groupId": "BG001", "value": "30"}, {"groupId": "BG002", "value": "28"}, {"groupId": "BG003", "value": "32"}, {"groupId": "BG004", "value": "28"}, {"groupId": "BG005", "value": "36"}, {"groupId": "BG006", "value": "27"}, {"groupId": "BG007", "value": "215"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "31"}, {"groupId": "BG001", "value": "34"}, {"groupId": "BG002", "value": "36"}, {"groupId": "BG003", "value": "33"}, {"groupId": "BG004", "value": "36"}, {"groupId": "BG005", "value": "28"}, {"groupId": "BG006", "value": "38"}, {"groupId": "BG007", "value": "236"}]}]}]}, {"title": "Region Enroll", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "ARGENTINA", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "1"}, {"groupId": "BG004", "value": "2"}, {"groupId": "BG005", "value": "0"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "10"}]}]}, {"title": "BULGARIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "0"}, {"groupId": "BG004", "value": "3"}, {"groupId": "BG005", "value": "3"}, {"groupId": "BG006", "value": "1"}, {"groupId": "BG007", "value": "10"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "8"}, {"groupId": "BG004", "value": "8"}, {"groupId": "BG005", "value": "4"}, {"groupId": "BG006", "value": "7"}, {"groupId": "BG007", "value": "53"}]}]}, {"title": "CZECH REPUBLIC", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "6"}, {"groupId": "BG004", "value": "0"}, {"groupId": "BG005", "value": "2"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "17"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "5"}, {"groupId": "BG004", "value": "2"}, {"groupId": "BG005", "value": "6"}, {"groupId": "BG006", "value": "4"}, {"groupId": "BG007", "value": "30"}]}]}, {"title": "MALAYSIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "2"}, {"groupId": "BG004", "value": "4"}, {"groupId": "BG005", "value": "1"}, {"groupId": "BG006", "value": "3"}, {"groupId": "BG007", "value": "19"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "9"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "14"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "50"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "5"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "7"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "5"}, {"groupId": "BG006", "value": "9"}, {"groupId": "BG007", "value": "39"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "9"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "6"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "5"}, {"groupId": "BG006", "value": "8"}, {"groupId": "BG007", "value": "46"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "2"}, {"groupId": "BG004", "value": "6"}, {"groupId": "BG005", "value": "3"}, {"groupId": "BG006", "value": "4"}, {"groupId": "BG007", "value": "32"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "1"}, {"groupId": "BG004", "value": "3"}, {"groupId": "BG005", "value": "1"}, {"groupId": "BG006", "value": "2"}, {"groupId": "BG007", "value": "10"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "21"}, {"groupId": "BG001", "value": "21"}, {"groupId": "BG002", "value": "16"}, {"groupId": "BG003", "value": "18"}, {"groupId": "BG004", "value": "18"}, {"groupId": "BG005", "value": "20"}, {"groupId": "BG006", "value": "21"}, {"groupId": "BG007", "value": "135"}]}]}]}]}