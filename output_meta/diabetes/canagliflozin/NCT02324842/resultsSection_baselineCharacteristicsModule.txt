# RESULTSSECTION - BASELINECHARACTERISTICSMODULE
============================================================

GROUPS:
  [1]
    id: BG000
    title: Canagliflozin
    description: canagliflozin (film-coated tablet), 100 mg/day, increased to 300 mg/day after week two if tolerated without side effects

Canagliflozin: Canagliflozin is a sodium-glucose co-transporter 2 (SGLT2) inhibitor indicated as an adjunct to diet and exercise to improve glycemic control in adults with type 2 diabetes mellitus (1)

  [2]
    id: BG001
    title: Liraglutide
    description: liraglutide, 1.2 mg/day, increased to 1.8 mg/day after week two if tolerated without side effects

Liraglutide: Liraglutide is a glucagon-like peptide-1 (GLP-1) receptor agonist indicated as an adjunct to diet and exercise to improve glycemic control in adults with type 2 diabetes mellitus.

  [3]
    id: BG002
    title: Canagliflozin Plus Liraglutide
    description: canagliflozin, 100 mg/day, plus liraglutide, 1.2 mg/day, increased to 300 mg/day and 1.8 mg/day, respectively at week two, if tolerated without side effects

Canagliflozin: Canagliflozin is a sodium-glucose co-transporter 2 (SGLT2) inhibitor indicated as an adjunct to diet and exercise to improve glycemic control in adults with type 2 diabetes mellitus (1)

Liraglutide: Liraglutide is a glucagon-like peptide-1 (GLP-1) receptor agonist indicated as an adjunct to diet and exercise to improve glycemic control in adults with type 2 diabetes mellitus.

  [4]
    id: BG003
    title: Total
    description: Total of all reporting groups


DENOMS:
  [1]
    units: Participants
    COUNTS:
      [1]
        groupId: BG000
        value: 14

      [2]
        groupId: BG001
        value: 14

      [3]
        groupId: BG002
        value: 13

      [4]
        groupId: BG003
        value: 41




MEASURES:
  [1]
    title: Age, Categorical
    paramType: COUNT_OF_PARTICIPANTS
    unitOfMeasure: Participants
    CLASSES:
      [1]
        DENOMS:
          [1]
            units: Participants
            COUNTS:
              [1]
                groupId: BG000
                value: 14

              [2]
                groupId: BG001
                value: 14

              [3]
                groupId: BG002
                value: 13

              [4]
                groupId: BG003
                value: 41




        CATEGORIES:
          [1]
            title: <=18 years
            MEASUREMENTS:
              [1]
                groupId: BG000
                value: 0

              [2]
                groupId: BG001
                value: 0

              [3]
                groupId: BG002
                value: 0

              [4]
                groupId: BG003
                value: 0



          [2]
            title: Between 18 and 65 years
            MEASUREMENTS:
              [1]
                groupId: BG000
                value: 14

              [2]
                groupId: BG001
                value: 14

              [3]
                groupId: BG002
                value: 13

              [4]
                groupId: BG003
                value: 41



          [3]
            title: >=65 years
            MEASUREMENTS:
              [1]
                groupId: BG000
                value: 0

              [2]
                groupId: BG001
                value: 0

              [3]
                groupId: BG002
                value: 0

              [4]
                groupId: BG003
                value: 0







  [2]
    title: Sex: Female, Male
    paramType: COUNT_OF_PARTICIPANTS
    unitOfMeasure: Participants
    CLASSES:
      [1]
        DENOMS:
          [1]
            units: Participants
            COUNTS:
              [1]
                groupId: BG000
                value: 14

              [2]
                groupId: BG001
                value: 14

              [3]
                groupId: BG002
                value: 13

              [4]
                groupId: BG003
                value: 41




        CATEGORIES:
          [1]
            title: Female
            MEASUREMENTS:
              [1]
                groupId: BG000
                value: 4

              [2]
                groupId: BG001
                value: 6

              [3]
                groupId: BG002
                value: 7

              [4]
                groupId: BG003
                value: 17



          [2]
            title: Male
            MEASUREMENTS:
              [1]
                groupId: BG000
                value: 10

              [2]
                groupId: BG001
                value: 8

              [3]
                groupId: BG002
                value: 6

              [4]
                groupId: BG003
                value: 24







  [3]
    title: Race and Ethnicity Not Collected
    populationDescription: Race and Ethnicity were not collected from any participant.
    paramType: COUNT_OF_PARTICIPANTS
    unitOfMeasure: Participants
    CLASSES:
      [1]
        DENOMS:
          [1]
            units: Participants
            COUNTS:
              [1]
                groupId: BG000
                value: 0

              [2]
                groupId: BG001
                value: 0

              [3]
                groupId: BG002
                value: 0

              [4]
                groupId: BG003
                value: 0




        CATEGORIES:
          [1]
            MEASUREMENTS:
              [1]
                groupId: BG003
                value: 0







  [4]
    title: Region of Enrollment
    paramType: NUMBER
    unitOfMeasure: participants
    CLASSES:
      [1]
        title: United States
        DENOMS:
          [1]
            units: Participants
            COUNTS:
              [1]
                groupId: BG000
                value: 14

              [2]
                groupId: BG001
                value: 14

              [3]
                groupId: BG002
                value: 13

              [4]
                groupId: BG003
                value: 41




        CATEGORIES:
          [1]
            MEASUREMENTS:
              [1]
                groupId: BG000
                value: 14

              [2]
                groupId: BG001
                value: 14

              [3]
                groupId: BG002
                value: 13

              [4]
                groupId: BG003
                value: 41







