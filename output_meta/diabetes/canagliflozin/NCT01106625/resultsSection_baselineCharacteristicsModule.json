{"groups": [{"id": "BG000", "title": "Placebo", "description": "Each patient received matching placebo once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once daily for 52 weeks with protocol-specified doses of metformin and sulphonylurea."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "156"}, {"groupId": "BG001", "value": "157"}, {"groupId": "BG002", "value": "156"}, {"groupId": "BG003", "value": "469"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "130"}, {"groupId": "BG001", "value": "121"}, {"groupId": "BG002", "value": "134"}, {"groupId": "BG003", "value": "385"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "26"}, {"groupId": "BG001", "value": "36"}, {"groupId": "BG002", "value": "22"}, {"groupId": "BG003", "value": "84"}]}]}]}, {"title": "Age Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "56.7", "spread": "8.36"}, {"groupId": "BG001", "value": "57.3", "spread": "10.47"}, {"groupId": "BG002", "value": "56", "spread": "8.95"}, {"groupId": "BG003", "value": "56.7", "spread": "9.3"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "80"}, {"groupId": "BG001", "value": "81"}, {"groupId": "BG002", "value": "69"}, {"groupId": "BG003", "value": "230"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "76"}, {"groupId": "BG001", "value": "76"}, {"groupId": "BG002", "value": "87"}, {"groupId": "BG003", "value": "239"}]}]}]}, {"title": "Region of Enrollment", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "AUSTRALIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "4"}, {"groupId": "BG003", "value": "13"}]}]}, {"title": "BELGIUM", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "6"}, {"groupId": "BG003", "value": "10"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "20"}]}]}, {"title": "GUATEMALA", "categories": [{"measurements": [{"groupId": "BG000", "value": "15"}, {"groupId": "BG001", "value": "12"}, {"groupId": "BG002", "value": "16"}, {"groupId": "BG003", "value": "43"}]}]}, {"title": "HUNGARY", "categories": [{"measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "14"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "36"}]}]}, {"title": "ISRAEL", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "15"}]}]}, {"title": "MEXICO", "categories": [{"measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "33"}]}]}, {"title": "RUSSIAN FEDERATION", "categories": [{"measurements": [{"groupId": "BG000", "value": "14"}, {"groupId": "BG001", "value": "13"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "35"}]}]}, {"title": "SPAIN", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "7"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "20"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "5"}, {"groupId": "BG003", "value": "19"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "81"}, {"groupId": "BG001", "value": "74"}, {"groupId": "BG002", "value": "70"}, {"groupId": "BG003", "value": "225"}]}]}]}]}