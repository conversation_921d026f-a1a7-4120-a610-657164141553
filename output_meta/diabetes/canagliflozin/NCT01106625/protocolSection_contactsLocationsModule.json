{"overallOfficials": [{"name": "Janssen Research & Development, LLC C. Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Huntsville", "state": "Alabama", "country": "United States", "geoPoint": {"lat": 34.7304, "lon": -86.58594}}, {"city": "Little Rock", "state": "Alaska", "country": "United States"}, {"city": "Phoenix", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.44838, "lon": -112.07404}}, {"city": "Chino", "state": "California", "country": "United States", "geoPoint": {"lat": 34.01223, "lon": -117.68894}}, {"city": "Fair Oaks", "state": "California", "country": "United States", "geoPoint": {"lat": 38.64463, "lon": -121.27217}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Wes Hills", "state": "California", "country": "United States", "geoPoint": {"lat": 34.19731, "lon": -118.64398}}, {"city": "Waterbury", "state": "Connecticut", "country": "United States", "geoPoint": {"lat": 41.55815, "lon": -73.0515}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "New Port Richey", "state": "Florida", "country": "United States", "geoPoint": {"lat": 28.24418, "lon": -82.71927}}, {"city": "Ocala", "state": "Florida", "country": "United States", "geoPoint": {"lat": 29.1872, "lon": -82.14009}}, {"city": "St Petersburg", "state": "Florida", "country": "United States", "geoPoint": {"lat": 27.77086, "lon": -82.67927}}, {"city": "West Palm Beach", "state": "Florida", "country": "United States", "geoPoint": {"lat": 26.71534, "lon": -80.05337}}, {"city": "Augusta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.47097, "lon": -81.97484}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 34.02316, "lon": -84.36159}}, {"city": "Chicago", "state": "Illinois", "country": "United States", "geoPoint": {"lat": 41.85003, "lon": -87.65005}}, {"city": "Lexington", "state": "Kentucky", "country": "United States", "geoPoint": {"lat": 37.98869, "lon": -84.47772}}, {"city": "New Orleans", "state": "Louisiana", "country": "United States", "geoPoint": {"lat": 29.95465, "lon": -90.07507}}, {"city": "Reisterstown", "state": "Maryland", "country": "United States", "geoPoint": {"lat": 39.46976, "lon": -76.8319}}, {"city": "Olive Branch", "state": "Mississippi", "country": "United States", "geoPoint": {"lat": 34.96176, "lon": -89.82953}}, {"city": "Saint Louis", "state": "Missouri", "country": "United States", "geoPoint": {"lat": 38.62727, "lon": -90.19789}}, {"city": "Westfield", "state": "New York", "country": "United States", "geoPoint": {"lat": 42.32228, "lon": -79.5781}}, {"city": "Greenville", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.61266, "lon": -77.36635}}, {"city": "Cincinnati", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.12713, "lon": -84.51435}}, {"city": "Tulsa", "state": "Oklahoma", "country": "United States", "geoPoint": {"lat": 36.15398, "lon": -95.99277}}, {"city": "Medford", "state": "Oregon", "country": "United States", "geoPoint": {"lat": 42.32652, "lon": -122.87559}}, {"city": "Altoona", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.51868, "lon": -78.39474}}, {"city": "Harleysville", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.27955, "lon": -75.38712}}, {"city": "Levittown", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.15511, "lon": -74.82877}}, {"city": "Sellersville", "state": "Pennsylvania", "country": "United States", "geoPoint": {"lat": 40.35399, "lon": -75.3049}}, {"city": "G<PERSON>", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 34.93873, "lon": -82.22706}}, {"city": "<PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 30.67436, "lon": -96.36996}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "<PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.58986, "lon": -96.85695}}, {"city": "Salt Lake City", "state": "Utah", "country": "United States", "geoPoint": {"lat": 40.76078, "lon": -111.89105}}, {"city": "South Burlington", "state": "Vermont", "country": "United States", "geoPoint": {"lat": 44.46699, "lon": -73.17096}}, {"city": "Wenatchee", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.42346, "lon": -120.31035}}, {"city": "Freemantle", "country": "Australia"}, {"city": "Heidelberg Heights", "country": "Australia", "geoPoint": {"lat": -37.74313, "lon": 145.05695}}, {"city": "Meadowbrook", "country": "Australia", "geoPoint": {"lat": -27.66401, "lon": 153.14465}}, {"city": "Nedlands", "country": "Australia", "geoPoint": {"lat": -31.98184, "lon": 115.8073}}, {"city": "Wollongong", "country": "Australia", "geoPoint": {"lat": -34.424, "lon": 150.89345}}, {"city": "Aalst", "country": "Belgium", "geoPoint": {"lat": 50.93604, "lon": 4.0355}}, {"city": "Bonheiden", "country": "Belgium", "geoPoint": {"lat": 51.02261, "lon": 4.54714}}, {"city": "<PERSON><PERSON><PERSON>", "country": "Belgium", "geoPoint": {"lat": 51.15662, "lon": 4.44504}}, {"city": "Leuven", "country": "Belgium", "geoPoint": {"lat": 50.87959, "lon": 4.70093}}, {"city": "<PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 48.60603, "lon": 2.48757}}, {"city": "La Rochelle", "country": "France", "geoPoint": {"lat": 46.16667, "lon": -1.15}}, {"city": "<PERSON>", "country": "France", "geoPoint": {"lat": 46.80714, "lon": 4.41632}}, {"city": "Poitiers", "country": "France", "geoPoint": {"lat": 46.58333, "lon": 0.33333}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 45.70254, "lon": 4.87147}}, {"city": "Guatemala", "country": "Guatemala", "geoPoint": {"lat": 14.64072, "lon": -90.51327}}, {"city": "Balatonfured", "country": "Hungary", "geoPoint": {"lat": 46.96188, "lon": 17.87187}}, {"city": "Budapest", "country": "Hungary", "geoPoint": {"lat": 47.49801, "lon": 19.03991}}, {"city": "Győr", "country": "Hungary", "geoPoint": {"lat": 47.68333, "lon": 17.63512}}, {"city": "Zalaegerszeg", "country": "Hungary", "geoPoint": {"lat": 46.84, "lon": 16.84389}}, {"city": "Haifa", "country": "Israel", "geoPoint": {"lat": 32.81841, "lon": 34.9885}}, {"city": "<PERSON>lon", "country": "Israel", "geoPoint": {"lat": 32.01034, "lon": 34.77918}}, {"city": "Jerusalem", "country": "Israel", "geoPoint": {"lat": 31.76904, "lon": 35.21633}}, {"city": "Tel Aviv", "country": "Israel", "geoPoint": {"lat": 32.08088, "lon": 34.78057}}, {"city": "Guadalajara", "country": "Mexico", "geoPoint": {"lat": 20.66682, "lon": -103.39182}}, {"city": "Monterrey", "country": "Mexico", "geoPoint": {"lat": 25.67507, "lon": -100.31847}}, {"city": "Carolina", "country": "Puerto Rico", "geoPoint": {"lat": 18.38078, "lon": -65.95739}}, {"city": "Trujillo Alto", "country": "Puerto Rico", "geoPoint": {"lat": 18.35467, "lon": -66.00739}}, {"city": "Arkhangelsk", "country": "Russian Federation", "geoPoint": {"lat": 64.5401, "lon": 40.5433}}, {"city": "Saint Petersburg", "country": "Russian Federation", "geoPoint": {"lat": 59.93863, "lon": 30.31413}}, {"city": "Samara", "country": "Russian Federation", "geoPoint": {"lat": 53.20007, "lon": 50.15}}, {"city": "Alicante", "country": "Spain", "geoPoint": {"lat": 38.34517, "lon": -0.48149}}, {"city": "Almeria", "country": "Spain", "geoPoint": {"lat": 36.83814, "lon": -2.45974}}, {"city": "Malaga", "country": "Spain", "geoPoint": {"lat": 36.72016, "lon": -4.42034}}, {"city": "Sevilla", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Valencia", "country": "Spain", "geoPoint": {"lat": 39.46975, "lon": -0.37739}}, {"city": "Belfast", "country": "United Kingdom", "geoPoint": {"lat": 54.59682, "lon": -5.92541}}, {"city": "Bolton", "country": "United Kingdom", "geoPoint": {"lat": 53.58333, "lon": -2.43333}}, {"city": "Liverpool", "country": "United Kingdom", "geoPoint": {"lat": 53.41058, "lon": -2.97794}}, {"city": "Manchester", "country": "United Kingdom", "geoPoint": {"lat": 53.48095, "lon": -2.23743}}]}