{"populationDescription": "Safety analysis set included all randomized participants who received at least 1 dose of study intervention.", "groups": [{"id": "BG000", "title": "Placebo", "description": "Participants received placebo (matched to canagliflozin) capsule orally once daily for 12 weeks."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Participants received canagliflozin 100 milligrams (mg) immediate-release, over-encapsulated tablet (as capsule) orally once daily for 12 weeks."}, {"id": "BG002", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "231"}, {"groupId": "BG001", "value": "224"}, {"groupId": "BG002", "value": "455"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "Years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "63.8", "spread": "13.5"}, {"groupId": "BG001", "value": "62.9", "spread": "13.15"}, {"groupId": "BG002", "value": "63.4", "spread": "13.32"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "99"}, {"groupId": "BG001", "value": "105"}, {"groupId": "BG002", "value": "204"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "132"}, {"groupId": "BG001", "value": "119"}, {"groupId": "BG002", "value": "251"}]}]}]}, {"title": "Race/Ethnicity, Customized", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "White", "measurements": [{"groupId": "BG000", "value": "199"}, {"groupId": "BG001", "value": "184"}, {"groupId": "BG002", "value": "383"}]}, {"title": "Black or African American", "measurements": [{"groupId": "BG000", "value": "30"}, {"groupId": "BG001", "value": "35"}, {"groupId": "BG002", "value": "65"}]}, {"title": "Asian", "measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "2"}]}, {"title": "Other", "measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "4"}, {"groupId": "BG002", "value": "5"}]}]}]}]}