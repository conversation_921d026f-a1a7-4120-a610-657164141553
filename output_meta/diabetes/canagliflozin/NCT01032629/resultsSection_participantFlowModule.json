{"preAssignmentDetails": "No Text enterred", "groups": [{"id": "FG000", "title": "Placebo", "description": "Participants received one placebo capsule orally once daily for the duration of the study or until early discontinuation from treatment."}, {"id": "FG001", "title": "Canagliflozin 100 mg", "description": "Participants received one canagliflozin 100 milligram (mg) capsule once daily for the duration of the study or until early discontinuation from treatment."}, {"id": "FG002", "title": "Canagliflozin 300 mg", "description": "Participants received one canagliflozin 300 mg capsule once daily for the duration of the study or until early discontinuation from treatment."}], "periods": [{"title": "Overall Study", "milestones": [{"type": "STARTED", "achievements": [{"groupId": "FG000", "numSubjects": "1442"}, {"groupId": "FG001", "numSubjects": "1445"}, {"groupId": "FG002", "numSubjects": "1443"}]}, {"type": "Treated", "achievements": [{"groupId": "FG000", "numSubjects": "1441"}, {"groupId": "FG001", "numSubjects": "1445"}, {"groupId": "FG002", "numSubjects": "1441"}]}, {"type": "COMPLETED", "achievements": [{"groupId": "FG000", "comment": "Participants who died on trial were considered completed", "numSubjects": "1297"}, {"groupId": "FG001", "comment": "Participants who died on trial were considered completed", "numSubjects": "1344"}, {"groupId": "FG002", "comment": "Participants who died on trial were considered completed", "numSubjects": "1355"}]}, {"type": "NOT COMPLETED", "achievements": [{"groupId": "FG000", "numSubjects": "145"}, {"groupId": "FG001", "numSubjects": "101"}, {"groupId": "FG002", "numSubjects": "88"}]}], "dropWithdraws": [{"type": "Closed Site", "reasons": [{"groupId": "FG000", "numSubjects": "21"}, {"groupId": "FG001", "numSubjects": "18"}, {"groupId": "FG002", "numSubjects": "16"}]}, {"type": "<PERSON><PERSON><PERSON> by Subject", "reasons": [{"groupId": "FG000", "numSubjects": "50"}, {"groupId": "FG001", "numSubjects": "36"}, {"groupId": "FG002", "numSubjects": "35"}]}, {"type": "Lost to Follow-up", "reasons": [{"groupId": "FG000", "numSubjects": "74"}, {"groupId": "FG001", "numSubjects": "47"}, {"groupId": "FG002", "numSubjects": "37"}]}]}]}