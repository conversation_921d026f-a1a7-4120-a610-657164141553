{"groups": [{"id": "BG000", "title": "Placebo", "description": "Participants received one placebo capsule orally once daily for the duration of the study or until early discontinuation from treatment."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Participants received one canagliflozin 100 milligram (mg) capsule once daily for the duration of the study or until early discontinuation from treatment."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Participants received one canagliflozin 300 mg capsule once daily for the duration of the study or until early discontinuation from treatment."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "1442"}, {"groupId": "BG001", "value": "1445"}, {"groupId": "BG002", "value": "1443"}, {"groupId": "BG003", "value": "4330"}]}], "measures": [{"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "Years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "62.3", "spread": "7.94"}, {"groupId": "BG001", "value": "62.2", "spread": "8"}, {"groupId": "BG002", "value": "62.8", "spread": "8.13"}, {"groupId": "BG003", "value": "62.4", "spread": "8.02"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "486"}, {"groupId": "BG001", "value": "484"}, {"groupId": "BG002", "value": "499"}, {"groupId": "BG003", "value": "1469"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "956"}, {"groupId": "BG001", "value": "961"}, {"groupId": "BG002", "value": "944"}, {"groupId": "BG003", "value": "2861"}]}]}]}, {"title": "Ethnicity (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "149"}, {"groupId": "BG001", "value": "142"}, {"groupId": "BG002", "value": "125"}, {"groupId": "BG003", "value": "416"}]}, {"title": "Not Hispanic or Latino", "measurements": [{"groupId": "BG000", "value": "1288"}, {"groupId": "BG001", "value": "1300"}, {"groupId": "BG002", "value": "1317"}, {"groupId": "BG003", "value": "3905"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "9"}]}]}]}, {"title": "Race (NIH/OMB)", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "American Indian or Alaska Native", "measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "5"}]}, {"title": "Asian", "measurements": [{"groupId": "BG000", "value": "262"}, {"groupId": "BG001", "value": "270"}, {"groupId": "BG002", "value": "263"}, {"groupId": "BG003", "value": "795"}]}, {"title": "Native Hawaiian or Other Pacific Islander", "measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "6"}]}, {"title": "Black or African American", "measurements": [{"groupId": "BG000", "value": "35"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "38"}, {"groupId": "BG003", "value": "105"}]}, {"title": "White", "measurements": [{"groupId": "BG000", "value": "1064"}, {"groupId": "BG001", "value": "1060"}, {"groupId": "BG002", "value": "1055"}, {"groupId": "BG003", "value": "3179"}]}, {"title": "More than one race", "measurements": [{"groupId": "BG000", "value": "10"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "13"}, {"groupId": "BG003", "value": "31"}]}, {"title": "Unknown or Not Reported", "measurements": [{"groupId": "BG000", "value": "67"}, {"groupId": "BG001", "value": "72"}, {"groupId": "BG002", "value": "70"}, {"groupId": "BG003", "value": "209"}]}]}]}, {"title": "Race/Ethnicity, Customized", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"title": "Asian", "categories": [{"measurements": [{"groupId": "BG000", "value": "262"}, {"groupId": "BG001", "value": "270"}, {"groupId": "BG002", "value": "263"}, {"groupId": "BG003", "value": "795"}]}]}, {"title": "Black or African American", "categories": [{"measurements": [{"groupId": "BG000", "value": "35"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "38"}, {"groupId": "BG003", "value": "105"}]}]}, {"title": "Hispanic or Latino", "categories": [{"measurements": [{"groupId": "BG000", "value": "134"}, {"groupId": "BG001", "value": "123"}, {"groupId": "BG002", "value": "106"}, {"groupId": "BG003", "value": "363"}]}]}, {"title": "Other", "categories": [{"measurements": [{"groupId": "BG000", "value": "82"}, {"groupId": "BG001", "value": "86"}, {"groupId": "BG002", "value": "88"}, {"groupId": "BG003", "value": "256"}]}]}, {"title": "White Non- Hispanic", "categories": [{"measurements": [{"groupId": "BG000", "value": "929"}, {"groupId": "BG001", "value": "934"}, {"groupId": "BG002", "value": "948"}, {"groupId": "BG003", "value": "2811"}]}]}]}, {"title": "Region of Enrollment", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"title": "Argentina", "categories": [{"measurements": [{"groupId": "BG000", "value": "57"}, {"groupId": "BG001", "value": "48"}, {"groupId": "BG002", "value": "55"}, {"groupId": "BG003", "value": "160"}]}]}, {"title": "Australia", "categories": [{"measurements": [{"groupId": "BG000", "value": "57"}, {"groupId": "BG001", "value": "60"}, {"groupId": "BG002", "value": "60"}, {"groupId": "BG003", "value": "177"}]}]}, {"title": "Belgium", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "10"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "21"}]}]}, {"title": "Canada", "categories": [{"measurements": [{"groupId": "BG000", "value": "134"}, {"groupId": "BG001", "value": "139"}, {"groupId": "BG002", "value": "123"}, {"groupId": "BG003", "value": "396"}]}]}, {"title": "Colombia", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "7"}]}]}, {"title": "Czech Republic", "categories": [{"measurements": [{"groupId": "BG000", "value": "37"}, {"groupId": "BG001", "value": "37"}, {"groupId": "BG002", "value": "43"}, {"groupId": "BG003", "value": "117"}]}]}, {"title": "Estonia", "categories": [{"measurements": [{"groupId": "BG000", "value": "19"}, {"groupId": "BG001", "value": "13"}, {"groupId": "BG002", "value": "12"}, {"groupId": "BG003", "value": "44"}]}]}, {"title": "Germany", "categories": [{"measurements": [{"groupId": "BG000", "value": "47"}, {"groupId": "BG001", "value": "62"}, {"groupId": "BG002", "value": "66"}, {"groupId": "BG003", "value": "175"}]}]}, {"title": "Hungary", "categories": [{"measurements": [{"groupId": "BG000", "value": "38"}, {"groupId": "BG001", "value": "50"}, {"groupId": "BG002", "value": "37"}, {"groupId": "BG003", "value": "125"}]}]}, {"title": "India", "categories": [{"measurements": [{"groupId": "BG000", "value": "229"}, {"groupId": "BG001", "value": "232"}, {"groupId": "BG002", "value": "234"}, {"groupId": "BG003", "value": "695"}]}]}, {"title": "Israel", "categories": [{"measurements": [{"groupId": "BG000", "value": "5"}, {"groupId": "BG001", "value": "11"}, {"groupId": "BG002", "value": "9"}, {"groupId": "BG003", "value": "25"}]}]}, {"title": "Luxembourg", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "1"}]}]}, {"title": "Malaysia", "categories": [{"measurements": [{"groupId": "BG000", "value": "32"}, {"groupId": "BG001", "value": "24"}, {"groupId": "BG002", "value": "17"}, {"groupId": "BG003", "value": "73"}]}]}, {"title": "Mexico", "categories": [{"measurements": [{"groupId": "BG000", "value": "40"}, {"groupId": "BG001", "value": "48"}, {"groupId": "BG002", "value": "36"}, {"groupId": "BG003", "value": "124"}]}]}, {"title": "Netherlands", "categories": [{"measurements": [{"groupId": "BG000", "value": "70"}, {"groupId": "BG001", "value": "77"}, {"groupId": "BG002", "value": "81"}, {"groupId": "BG003", "value": "228"}]}]}, {"title": "New Zealand", "categories": [{"measurements": [{"groupId": "BG000", "value": "27"}, {"groupId": "BG001", "value": "22"}, {"groupId": "BG002", "value": "25"}, {"groupId": "BG003", "value": "74"}]}]}, {"title": "Norway", "categories": [{"measurements": [{"groupId": "BG000", "value": "41"}, {"groupId": "BG001", "value": "33"}, {"groupId": "BG002", "value": "35"}, {"groupId": "BG003", "value": "109"}]}]}, {"title": "Poland", "categories": [{"measurements": [{"groupId": "BG000", "value": "47"}, {"groupId": "BG001", "value": "54"}, {"groupId": "BG002", "value": "43"}, {"groupId": "BG003", "value": "144"}]}]}, {"title": "Russia", "categories": [{"measurements": [{"groupId": "BG000", "value": "146"}, {"groupId": "BG001", "value": "118"}, {"groupId": "BG002", "value": "125"}, {"groupId": "BG003", "value": "389"}]}]}, {"title": "Spain", "categories": [{"measurements": [{"groupId": "BG000", "value": "64"}, {"groupId": "BG001", "value": "74"}, {"groupId": "BG002", "value": "71"}, {"groupId": "BG003", "value": "209"}]}]}, {"title": "Sweden", "categories": [{"measurements": [{"groupId": "BG000", "value": "23"}, {"groupId": "BG001", "value": "26"}, {"groupId": "BG002", "value": "22"}, {"groupId": "BG003", "value": "71"}]}]}, {"title": "Ukraine", "categories": [{"measurements": [{"groupId": "BG000", "value": "50"}, {"groupId": "BG001", "value": "39"}, {"groupId": "BG002", "value": "58"}, {"groupId": "BG003", "value": "147"}]}]}, {"title": "United Kingdom", "categories": [{"measurements": [{"groupId": "BG000", "value": "31"}, {"groupId": "BG001", "value": "29"}, {"groupId": "BG002", "value": "32"}, {"groupId": "BG003", "value": "92"}]}]}, {"title": "United States", "categories": [{"measurements": [{"groupId": "BG000", "value": "240"}, {"groupId": "BG001", "value": "238"}, {"groupId": "BG002", "value": "249"}, {"groupId": "BG003", "value": "727"}]}]}]}]}