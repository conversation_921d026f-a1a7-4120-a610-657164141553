{"overallOfficials": [{"name": "Janssen Research & Development, LLC Clinical Trial", "affiliation": "Janssen Research & Development, LLC", "role": "STUDY_DIRECTOR"}], "locations": [{"city": "Glendale", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.53865, "lon": -112.18599}}, {"city": "Phoenix", "state": "Arizona", "country": "United States", "geoPoint": {"lat": 33.44838, "lon": -112.07404}}, {"city": "Little Rock", "state": "Arkansas", "country": "United States", "geoPoint": {"lat": 34.74648, "lon": -92.28959}}, {"city": "<PERSON>", "state": "California", "country": "United States", "geoPoint": {"lat": 38.61713, "lon": -121.32828}}, {"city": "Citrus Heights", "state": "California", "country": "United States", "geoPoint": {"lat": 38.70712, "lon": -121.28106}}, {"city": "Fair Oaks", "state": "California", "country": "United States", "geoPoint": {"lat": 38.64463, "lon": -121.27217}}, {"city": "Roseville", "state": "California", "country": "United States", "geoPoint": {"lat": 38.75212, "lon": -121.28801}}, {"city": "Sacramento", "state": "California", "country": "United States", "geoPoint": {"lat": 38.58157, "lon": -121.4944}}, {"city": "San Diego", "state": "California", "country": "United States", "geoPoint": {"lat": 32.71533, "lon": -117.15726}}, {"city": "Walnut Creek", "state": "California", "country": "United States", "geoPoint": {"lat": 37.90631, "lon": -122.06496}}, {"city": "Daytona Beach", "state": "Florida", "country": "United States", "geoPoint": {"lat": 29.21081, "lon": -81.02283}}, {"city": "Fleming Island", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.0933, "lon": -81.71898}}, {"city": "Jacksonville", "state": "Florida", "country": "United States", "geoPoint": {"lat": 30.33218, "lon": -81.65565}}, {"city": "Miami", "state": "Florida", "country": "United States", "geoPoint": {"lat": 25.77427, "lon": -80.19366}}, {"city": "Atlanta", "state": "Georgia", "country": "United States", "geoPoint": {"lat": 33.749, "lon": -84.38798}}, {"city": "Wichita", "state": "Kansas", "country": "United States", "geoPoint": {"lat": 37.69224, "lon": -97.33754}}, {"city": "<PERSON><PERSON>", "state": "Massachusetts", "country": "United States", "geoPoint": {"lat": 42.37649, "lon": -71.23561}}, {"city": "<PERSON><PERSON><PERSON>", "state": "Nevada", "country": "United States", "geoPoint": {"lat": 36.20829, "lon": -115.98391}}, {"city": "Albuquerque", "state": "New Mexico", "country": "United States", "geoPoint": {"lat": 35.08449, "lon": -106.65114}}, {"city": "<PERSON>", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.79154, "lon": -78.78112}}, {"city": "Charlotte", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 35.22709, "lon": -80.84313}}, {"city": "Wilmington", "state": "North Carolina", "country": "United States", "geoPoint": {"lat": 34.22573, "lon": -77.94471}}, {"city": "Bismarck", "state": "North Dakota", "country": "United States", "geoPoint": {"lat": 46.80833, "lon": -100.78374}}, {"city": "<PERSON>", "state": "Ohio", "country": "United States", "geoPoint": {"lat": 39.55895, "lon": -84.30411}}, {"city": "Mount Pleasant", "state": "South Carolina", "country": "United States", "geoPoint": {"lat": 32.79407, "lon": -79.86259}}, {"city": "Bristol", "state": "Tennessee", "country": "United States", "geoPoint": {"lat": 36.59511, "lon": -82.18874}}, {"city": "<PERSON><PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.95373, "lon": -96.89028}}, {"city": "Dallas", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.78306, "lon": -96.80667}}, {"city": "<PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.81402, "lon": -96.94889}}, {"city": "Plano", "state": "Texas", "country": "United States", "geoPoint": {"lat": 33.01984, "lon": -96.69889}}, {"city": "<PERSON>", "state": "Texas", "country": "United States", "geoPoint": {"lat": 32.94818, "lon": -96.72972}}, {"city": "Ren<PERSON>", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.48288, "lon": -122.21707}}, {"city": "Tacoma", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.25288, "lon": -122.44429}}, {"city": "Wenatchee", "state": "Washington", "country": "United States", "geoPoint": {"lat": 47.42346, "lon": -120.31035}}, {"city": "Fremantle", "country": "Australia", "geoPoint": {"lat": -32.05629, "lon": 115.74565}}, {"city": "Heidelberg Heights", "country": "Australia", "geoPoint": {"lat": -37.74313, "lon": 145.05695}}, {"city": "Meadowbrook", "country": "Australia", "geoPoint": {"lat": -27.66401, "lon": 153.14465}}, {"city": "Richmond", "country": "Australia", "geoPoint": {"lat": -37.81819, "lon": 145.00176}}, {"city": "Vancouver", "state": "British Columbia", "country": "Canada", "geoPoint": {"lat": 49.24966, "lon": -123.11934}}, {"city": "St. John'S", "state": "Newfoundland and Labrador", "country": "Canada", "geoPoint": {"lat": 47.56494, "lon": -52.70931}}, {"city": "<PERSON><PERSON>", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 44.40011, "lon": -79.66634}}, {"city": "London", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 42.98339, "lon": -81.23304}}, {"city": "Markham", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.86682, "lon": -79.2663}}, {"city": "Oakville", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.45011, "lon": -79.68292}}, {"city": "Toronto", "state": "Ontario", "country": "Canada", "geoPoint": {"lat": 43.70011, "lon": -79.4163}}, {"city": "Montreal", "state": "Quebec", "country": "Canada", "geoPoint": {"lat": 45.50884, "lon": -73.58781}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "Colombia", "geoPoint": {"lat": 10.96854, "lon": -74.78132}}, {"city": "Bogota", "country": "Colombia", "geoPoint": {"lat": 4.60971, "lon": -74.08175}}, {"city": "<PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 48.60603, "lon": 2.48757}}, {"city": "Paris", "country": "France", "geoPoint": {"lat": 48.85341, "lon": 2.3488}}, {"city": "<PERSON><PERSON><PERSON><PERSON>", "country": "France", "geoPoint": {"lat": 45.70254, "lon": 4.87147}}, {"city": "Thessalonikis", "country": "Greece"}, {"city": "Thessaloniki", "country": "Greece", "geoPoint": {"lat": 40.64361, "lon": 22.93086}}, {"city": "<PERSON><PERSON>", "country": "Hong Kong", "geoPoint": {"lat": 22.38333, "lon": 114.18333}}, {"city": "Bangalore", "country": "India", "geoPoint": {"lat": 12.97194, "lon": 77.59369}}, {"city": "Nagpur", "country": "India", "geoPoint": {"lat": 21.14631, "lon": 79.08491}}, {"city": "Pune", "country": "India", "geoPoint": {"lat": 18.51957, "lon": 73.85535}}, {"city": "Auckland", "country": "New Zealand", "geoPoint": {"lat": -36.84853, "lon": 174.76349}}, {"city": "Christchurch", "country": "New Zealand", "geoPoint": {"lat": -43.53333, "lon": 172.63333}}, {"city": "Taurang<PERSON>", "country": "New Zealand", "geoPoint": {"lat": -37.68611, "lon": 176.16667}}, {"city": "Wellington", "country": "New Zealand", "geoPoint": {"lat": -41.28664, "lon": 174.77557}}, {"city": "Katowice", "country": "Poland", "geoPoint": {"lat": 50.25841, "lon": 19.02754}}, {"city": "Krakow", "country": "Poland", "geoPoint": {"lat": 50.06143, "lon": 19.93658}}, {"city": "<PERSON><PERSON>", "country": "Poland", "geoPoint": {"lat": 53.01375, "lon": 18.59814}}, {"city": "Warszawa", "country": "Poland", "geoPoint": {"lat": 52.22977, "lon": 21.01178}}, {"city": "Wroclaw", "country": "Poland", "geoPoint": {"lat": 51.1, "lon": 17.03333}}, {"city": "Bucharest", "country": "Romania", "geoPoint": {"lat": 44.43225, "lon": 26.10626}}, {"city": "Sibiu", "country": "Romania", "geoPoint": {"lat": 45.8, "lon": 24.15}}, {"city": "Pretoria", "country": "South Africa", "geoPoint": {"lat": -25.74486, "lon": 28.18783}}, {"city": "Granada", "country": "Spain", "geoPoint": {"lat": 37.18817, "lon": -3.60667}}, {"city": "Madrid", "country": "Spain", "geoPoint": {"lat": 40.4165, "lon": -3.70256}}, {"city": "Pozuelo De Alarcon", "country": "Spain", "geoPoint": {"lat": 40.43293, "lon": -3.81338}}, {"city": "Sevilla", "country": "Spain", "geoPoint": {"lat": 37.38283, "lon": -5.97317}}, {"city": "Göteborg", "country": "Sweden", "geoPoint": {"lat": 57.70716, "lon": 11.96679}}, {"city": "Uppsala", "country": "Sweden", "geoPoint": {"lat": 59.85882, "lon": 17.63889}}, {"city": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "country": "Switzerland", "geoPoint": {"lat": 47.5296, "lon": 7.59902}}, {"city": "St Gallen", "country": "Switzerland", "geoPoint": {"lat": 47.42391, "lon": 9.37477}}, {"city": "Kharkov", "country": "Ukraine", "geoPoint": {"lat": 49.98081, "lon": 36.25272}}, {"city": "Kiev", "country": "Ukraine", "geoPoint": {"lat": 50.45466, "lon": 30.5238}}, {"city": "Birmingham", "country": "United Kingdom", "geoPoint": {"lat": 52.48142, "lon": -1.89983}}, {"city": "Cardiff", "country": "United Kingdom", "geoPoint": {"lat": 51.48, "lon": -3.18}}, {"city": "Glasgow", "country": "United Kingdom", "geoPoint": {"lat": 55.86515, "lon": -4.25763}}, {"city": "Liverpool", "country": "United Kingdom", "geoPoint": {"lat": 53.41058, "lon": -2.97794}}, {"city": "Manchester", "country": "United Kingdom", "geoPoint": {"lat": 53.48095, "lon": -2.23743}}, {"city": "Reading", "country": "United Kingdom", "geoPoint": {"lat": 51.45625, "lon": -0.97113}}, {"city": "Salford", "country": "United Kingdom", "geoPoint": {"lat": 53.48771, "lon": -2.29042}}]}