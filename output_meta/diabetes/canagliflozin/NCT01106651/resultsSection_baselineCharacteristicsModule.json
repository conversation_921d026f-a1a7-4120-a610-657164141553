{"groups": [{"id": "BG000", "title": "Placebo", "description": "Each patient received matching placebo once daily for 104 weeks in addition to being on a stable antihyperglycemic (AHA) regimen at the time of study entry."}, {"id": "BG001", "title": "Canagliflozin 100 mg", "description": "Each patient received 100 mg of canagliflozin once daily for 104 weeks in addition to being on a stable antihyperglycemic (AHA) regimen at the time of study entry."}, {"id": "BG002", "title": "Canagliflozin 300 mg", "description": "Each patient received 300 mg of canagliflozin once daily for 104 weeks in addition to being on a stable antihyperglycemic (AHA) regimen at the time of study entry."}, {"id": "BG003", "title": "Total", "description": "Total of all reporting groups"}], "denoms": [{"units": "Participants", "counts": [{"groupId": "BG000", "value": "237"}, {"groupId": "BG001", "value": "241"}, {"groupId": "BG002", "value": "236"}, {"groupId": "BG003", "value": "714"}]}], "measures": [{"title": "Age, Categorical", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "<=18 years", "measurements": [{"groupId": "BG000", "value": "0"}, {"groupId": "BG001", "value": "0"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "0"}]}, {"title": "Between 18 and 65 years", "measurements": [{"groupId": "BG000", "value": "151"}, {"groupId": "BG001", "value": "141"}, {"groupId": "BG002", "value": "149"}, {"groupId": "BG003", "value": "441"}]}, {"title": ">=65 years", "measurements": [{"groupId": "BG000", "value": "86"}, {"groupId": "BG001", "value": "100"}, {"groupId": "BG002", "value": "87"}, {"groupId": "BG003", "value": "273"}]}]}]}, {"title": "Age, Continuous", "paramType": "MEAN", "dispersionType": "STANDARD_DEVIATION", "unitOfMeasure": "years", "classes": [{"categories": [{"measurements": [{"groupId": "BG000", "value": "63.2", "spread": "6.21"}, {"groupId": "BG001", "value": "64.3", "spread": "6.46"}, {"groupId": "BG002", "value": "63.4", "spread": "5.99"}, {"groupId": "BG003", "value": "63.6", "spread": "6.24"}]}]}]}, {"title": "Sex: Female, Male", "paramType": "COUNT_OF_PARTICIPANTS", "unitOfMeasure": "Participants", "classes": [{"categories": [{"title": "Female", "measurements": [{"groupId": "BG000", "value": "94"}, {"groupId": "BG001", "value": "117"}, {"groupId": "BG002", "value": "107"}, {"groupId": "BG003", "value": "318"}]}, {"title": "Male", "measurements": [{"groupId": "BG000", "value": "143"}, {"groupId": "BG001", "value": "124"}, {"groupId": "BG002", "value": "129"}, {"groupId": "BG003", "value": "396"}]}]}]}, {"title": "Region of Enrollment", "paramType": "NUMBER", "unitOfMeasure": "participants", "classes": [{"title": "AUSTRALIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "6"}, {"groupId": "BG001", "value": "6"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "23"}]}]}, {"title": "CANADA", "categories": [{"measurements": [{"groupId": "BG000", "value": "24"}, {"groupId": "BG001", "value": "32"}, {"groupId": "BG002", "value": "28"}, {"groupId": "BG003", "value": "84"}]}]}, {"title": "COLOMBIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "18"}, {"groupId": "BG001", "value": "15"}, {"groupId": "BG002", "value": "20"}, {"groupId": "BG003", "value": "53"}]}]}, {"title": "FRANCE", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "7"}]}]}, {"title": "GREECE", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "1"}, {"groupId": "BG003", "value": "3"}]}]}, {"title": "HONG KONG", "categories": [{"measurements": [{"groupId": "BG000", "value": "1"}, {"groupId": "BG001", "value": "1"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "4"}]}]}, {"title": "INDIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "22"}]}]}, {"title": "NEW ZEALAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "16"}, {"groupId": "BG001", "value": "10"}, {"groupId": "BG002", "value": "11"}, {"groupId": "BG003", "value": "37"}]}]}, {"title": "POLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "11"}, {"groupId": "BG001", "value": "12"}, {"groupId": "BG002", "value": "14"}, {"groupId": "BG003", "value": "37"}]}]}, {"title": "ROMANIA", "categories": [{"measurements": [{"groupId": "BG000", "value": "8"}, {"groupId": "BG001", "value": "10"}, {"groupId": "BG002", "value": "7"}, {"groupId": "BG003", "value": "25"}]}]}, {"title": "SOUTH AFRICA", "categories": [{"measurements": [{"groupId": "BG000", "value": "9"}, {"groupId": "BG001", "value": "12"}, {"groupId": "BG002", "value": "10"}, {"groupId": "BG003", "value": "31"}]}]}, {"title": "SPAIN", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "3"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "13"}]}]}, {"title": "SWEDEN", "categories": [{"measurements": [{"groupId": "BG000", "value": "4"}, {"groupId": "BG001", "value": "4"}, {"groupId": "BG002", "value": "2"}, {"groupId": "BG003", "value": "10"}]}]}, {"title": "SWITZERLAND", "categories": [{"measurements": [{"groupId": "BG000", "value": "2"}, {"groupId": "BG001", "value": "2"}, {"groupId": "BG002", "value": "0"}, {"groupId": "BG003", "value": "4"}]}]}, {"title": "UKRAINE", "categories": [{"measurements": [{"groupId": "BG000", "value": "3"}, {"groupId": "BG001", "value": "8"}, {"groupId": "BG002", "value": "3"}, {"groupId": "BG003", "value": "14"}]}]}, {"title": "UNITED KINGDOM", "categories": [{"measurements": [{"groupId": "BG000", "value": "19"}, {"groupId": "BG001", "value": "22"}, {"groupId": "BG002", "value": "8"}, {"groupId": "BG003", "value": "49"}]}]}, {"title": "UNITED STATES", "categories": [{"measurements": [{"groupId": "BG000", "value": "103"}, {"groupId": "BG001", "value": "98"}, {"groupId": "BG002", "value": "97"}, {"groupId": "BG003", "value": "298"}]}]}]}]}