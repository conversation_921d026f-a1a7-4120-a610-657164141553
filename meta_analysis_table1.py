#!/usr/bin/env python3
"""
Meta-Analysis Table 1 Generation Module

This module extracts ALL raw variables directly from JSON modules of clinical trials data
and generates comprehensive meta-variables for robust meta-analysis.

Features:
- Raw variables: Direct extraction from JSON modules (122 variables)
- Meta-variables: Derived variables for subgroup and meta-regression analysis (52 variables)
- Comprehensive CSV outputs for both raw and meta-variables

Author: Clinical Trials Analysis System
Version: 4.0 - Raw Variables + Meta-Variables Implementation
"""

import json
import os
import csv
from pathlib import Path


def extract_raw_variables_from_json(study_path, study_id):
    """
    Extract ALL raw variables directly from JSON modules
    
    This function reads all JSON module files and extracts structured data
    into a comprehensive set of raw variables for meta-analysis.
    
    Args:
        study_path (str): Path to NCT study folder containing JSON modules
        study_id (str): NCT study identifier
        
    Returns:
        dict: Dictionary containing all raw variables extracted from JSON modules
    """
    
    # Initialize raw variables dictionary with study ID
    raw_vars = {'study_id': study_id}
    
    # Read all JSON module files
    json_modules = {}
    for file_name in os.listdir(study_path):
        if file_name.endswith('.json'):
            module_name = file_name.replace('.json', '')
            file_path = os.path.join(study_path, file_name)
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    json_modules[module_name] = json.load(f)
            except Exception as e:
                print(f"⚠️ Could not read {file_path}: {e}")
                json_modules[module_name] = {}
    
    # Extract variables from each module
    extract_identification_module_vars(raw_vars, json_modules)
    extract_status_module_vars(raw_vars, json_modules)
    extract_design_module_vars(raw_vars, json_modules)
    extract_arms_interventions_module_vars(raw_vars, json_modules)
    extract_conditions_module_vars(raw_vars, json_modules)
    extract_eligibility_module_vars(raw_vars, json_modules)
    extract_contacts_locations_module_vars(raw_vars, json_modules)
    extract_outcomes_module_vars(raw_vars, json_modules)
    extract_sponsor_collaborators_module_vars(raw_vars, json_modules)
    extract_oversight_module_vars(raw_vars, json_modules)
    extract_description_module_vars(raw_vars, json_modules)
    extract_references_module_vars(raw_vars, json_modules)
    extract_baseline_characteristics_module_vars(raw_vars, json_modules)
    extract_participant_flow_module_vars(raw_vars, json_modules)
    extract_outcome_measures_module_vars(raw_vars, json_modules)
    extract_adverse_events_module_vars(raw_vars, json_modules)
    extract_more_info_module_vars(raw_vars, json_modules)
    extract_derived_section_vars(raw_vars, json_modules)
    
    return raw_vars


def extract_identification_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_identificationModule"""
    
    module_key = 'protocolSection_identificationModule'
    if module_key not in json_modules:
        # Set all variables to NA if module doesn't exist
        raw_vars.update({
            'nctId': 'NA',
            'orgStudyIdInfo_id': 'NA',
            'secondaryIdInfos_count': 'NA',
            'secondaryIdInfos_types': 'NA',
            'secondaryIdInfos_domains': 'NA',
            'organization_fullName': 'NA',
            'organization_class': 'NA',
            'briefTitle': 'NA',
            'officialTitle': 'NA'
        })
        return
        
    module = json_modules[module_key]
    
    # Direct field extractions
    raw_vars['nctId'] = module.get('nctId', 'NA')
    raw_vars['briefTitle'] = module.get('briefTitle', 'NA')
    raw_vars['officialTitle'] = module.get('officialTitle', 'NA')
    
    # Nested object extractions
    org_study_id = module.get('orgStudyIdInfo', {})
    raw_vars['orgStudyIdInfo_id'] = org_study_id.get('id', 'NA')
    
    organization = module.get('organization', {})
    raw_vars['organization_fullName'] = organization.get('fullName', 'NA')
    raw_vars['organization_class'] = organization.get('class', 'NA')
    
    # Array extractions - secondaryIdInfos
    secondary_ids = module.get('secondaryIdInfos', [])
    if secondary_ids:
        raw_vars['secondaryIdInfos_count'] = len(secondary_ids)
        raw_vars['secondaryIdInfos_types'] = ','.join([sid.get('type', '') for sid in secondary_ids])
        raw_vars['secondaryIdInfos_domains'] = ','.join([sid.get('domain', '') for sid in secondary_ids])
    else:
        raw_vars['secondaryIdInfos_count'] = 0
        raw_vars['secondaryIdInfos_types'] = 'NA'
        raw_vars['secondaryIdInfos_domains'] = 'NA'


def extract_status_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_statusModule"""
    
    module_key = 'protocolSection_statusModule'
    if module_key not in json_modules:
        raw_vars.update({
            'statusVerifiedDate': 'NA', 'overallStatus': 'NA', 'expandedAccessInfo_hasExpandedAccess': 'NA',
            'startDateStruct_date': 'NA', 'startDateStruct_type': 'NA',
            'primaryCompletionDateStruct_date': 'NA', 'primaryCompletionDateStruct_type': 'NA',
            'completionDateStruct_date': 'NA', 'completionDateStruct_type': 'NA',
            'studyFirstSubmitDate': 'NA', 'studyFirstSubmitQcDate': 'NA',
            'studyFirstPostDateStruct_date': 'NA', 'studyFirstPostDateStruct_type': 'NA',
            'resultsFirstSubmitDate': 'NA', 'resultsFirstSubmitQcDate': 'NA',
            'resultsFirstPostDateStruct_date': 'NA', 'resultsFirstPostDateStruct_type': 'NA',
            'lastUpdateSubmitDate': 'NA', 'lastUpdatePostDateStruct_date': 'NA', 'lastUpdatePostDateStruct_type': 'NA'
        })
        return
        
    module = json_modules[module_key]
    raw_vars['statusVerifiedDate'] = module.get('statusVerifiedDate', 'NA')
    raw_vars['overallStatus'] = module.get('overallStatus', 'NA')
    raw_vars['studyFirstSubmitDate'] = module.get('studyFirstSubmitDate', 'NA')
    raw_vars['studyFirstSubmitQcDate'] = module.get('studyFirstSubmitQcDate', 'NA')
    raw_vars['resultsFirstSubmitDate'] = module.get('resultsFirstSubmitDate', 'NA')
    raw_vars['resultsFirstSubmitQcDate'] = module.get('resultsFirstSubmitQcDate', 'NA')
    raw_vars['lastUpdateSubmitDate'] = module.get('lastUpdateSubmitDate', 'NA')
    
    expanded_access = module.get('expandedAccessInfo', {})
    raw_vars['expandedAccessInfo_hasExpandedAccess'] = expanded_access.get('hasExpandedAccess', 'NA')
    
    start_date_struct = module.get('startDateStruct', {})
    raw_vars['startDateStruct_date'] = start_date_struct.get('date', 'NA')
    raw_vars['startDateStruct_type'] = start_date_struct.get('type', 'NA')
    
    primary_completion_date_struct = module.get('primaryCompletionDateStruct', {})
    raw_vars['primaryCompletionDateStruct_date'] = primary_completion_date_struct.get('date', 'NA')
    raw_vars['primaryCompletionDateStruct_type'] = primary_completion_date_struct.get('type', 'NA')
    
    completion_date_struct = module.get('completionDateStruct', {})
    raw_vars['completionDateStruct_date'] = completion_date_struct.get('date', 'NA')
    raw_vars['completionDateStruct_type'] = completion_date_struct.get('type', 'NA')
    
    study_first_post_date_struct = module.get('studyFirstPostDateStruct', {})
    raw_vars['studyFirstPostDateStruct_date'] = study_first_post_date_struct.get('date', 'NA')
    raw_vars['studyFirstPostDateStruct_type'] = study_first_post_date_struct.get('type', 'NA')
    
    results_first_post_date_struct = module.get('resultsFirstPostDateStruct', {})
    raw_vars['resultsFirstPostDateStruct_date'] = results_first_post_date_struct.get('date', 'NA')
    raw_vars['resultsFirstPostDateStruct_type'] = results_first_post_date_struct.get('type', 'NA')
    
    last_update_post_date_struct = module.get('lastUpdatePostDateStruct', {})
    raw_vars['lastUpdatePostDateStruct_date'] = last_update_post_date_struct.get('date', 'NA')
    raw_vars['lastUpdatePostDateStruct_type'] = last_update_post_date_struct.get('type', 'NA')


def extract_design_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_designModule"""
    
    module_key = 'protocolSection_designModule'
    if module_key not in json_modules:
        raw_vars.update({
            'studyType': 'NA', 'phases': 'NA', 'phases_count': 'NA',
            'designInfo_allocation': 'NA', 'designInfo_interventionModel': 'NA', 'designInfo_primaryPurpose': 'NA',
            'designInfo_maskingInfo_masking': 'NA', 'designInfo_maskingInfo_whoMasked': 'NA', 'designInfo_maskingInfo_whoMasked_count': 'NA',
            'enrollmentInfo_count': 'NA', 'enrollmentInfo_type': 'NA'
        })
        return
        
    module = json_modules[module_key]
    raw_vars['studyType'] = module.get('studyType', 'NA')
    raw_vars['enrollmentInfo_count'] = module.get('enrollmentInfo', {}).get('count', 'NA')
    raw_vars['enrollmentInfo_type'] = module.get('enrollmentInfo', {}).get('type', 'NA')
    
    # Phases array
    phases = module.get('phases', [])
    if phases:
        raw_vars['phases'] = ','.join(phases)
        raw_vars['phases_count'] = len(phases)
    else:
        raw_vars['phases'] = 'NA'
        raw_vars['phases_count'] = 0
    
    # Design info nested object
    design_info = module.get('designInfo', {})
    raw_vars['designInfo_allocation'] = design_info.get('allocation', 'NA')
    raw_vars['designInfo_interventionModel'] = design_info.get('interventionModel', 'NA')
    raw_vars['designInfo_primaryPurpose'] = design_info.get('primaryPurpose', 'NA')
    
    # Masking info nested object
    masking_info = design_info.get('maskingInfo', {})
    raw_vars['designInfo_maskingInfo_masking'] = masking_info.get('masking', 'NA')
    who_masked = masking_info.get('whoMasked', [])
    if who_masked:
        raw_vars['designInfo_maskingInfo_whoMasked'] = ','.join(who_masked)
        raw_vars['designInfo_maskingInfo_whoMasked_count'] = len(who_masked)
    else:
        raw_vars['designInfo_maskingInfo_whoMasked'] = 'NA'
        raw_vars['designInfo_maskingInfo_whoMasked_count'] = 0


# Placeholder functions for remaining modules (to be implemented)
def extract_arms_interventions_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_armsInterventionsModule"""

    module_key = 'protocolSection_armsInterventionsModule'
    if module_key not in json_modules:
        raw_vars.update({
            'armGroups_count': 'NA', 'armGroups_types': 'NA', 'armGroups_labels': 'NA',
            'interventions_count': 'NA', 'interventions_types': 'NA', 'interventions_names': 'NA'
        })
        return

    module = json_modules[module_key]

    # Arm groups array
    arm_groups = module.get('armGroups', [])
    if arm_groups:
        raw_vars['armGroups_count'] = len(arm_groups)
        raw_vars['armGroups_types'] = ','.join([ag.get('type', '') for ag in arm_groups])
        raw_vars['armGroups_labels'] = ','.join([ag.get('label', '') for ag in arm_groups])
    else:
        raw_vars['armGroups_count'] = 0
        raw_vars['armGroups_types'] = 'NA'
        raw_vars['armGroups_labels'] = 'NA'

    # Interventions array
    interventions = module.get('interventions', [])
    if interventions:
        raw_vars['interventions_count'] = len(interventions)
        raw_vars['interventions_types'] = ','.join([iv.get('type', '') for iv in interventions])
        raw_vars['interventions_names'] = ','.join([iv.get('name', '') for iv in interventions])
    else:
        raw_vars['interventions_count'] = 0
        raw_vars['interventions_types'] = 'NA'
        raw_vars['interventions_names'] = 'NA'


def extract_conditions_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_conditionsModule"""

    module_key = 'protocolSection_conditionsModule'
    if module_key not in json_modules:
        raw_vars.update({
            'conditions_count': 'NA', 'conditions': 'NA',
            'keywords_count': 'NA', 'keywords': 'NA'
        })
        return

    module = json_modules[module_key]

    # Conditions array
    conditions = module.get('conditions', [])
    if conditions:
        raw_vars['conditions_count'] = len(conditions)
        raw_vars['conditions'] = ','.join(conditions)
    else:
        raw_vars['conditions_count'] = 0
        raw_vars['conditions'] = 'NA'

    # Keywords array
    keywords = module.get('keywords', [])
    if keywords:
        raw_vars['keywords_count'] = len(keywords)
        raw_vars['keywords'] = ','.join(keywords)
    else:
        raw_vars['keywords_count'] = 0
        raw_vars['keywords'] = 'NA'


def extract_eligibility_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_eligibilityModule"""

    module_key = 'protocolSection_eligibilityModule'
    if module_key not in json_modules:
        raw_vars.update({
            'healthyVolunteers': 'NA', 'sex': 'NA', 'minimumAge': 'NA', 'maximumAge': 'NA',
            'stdAges_count': 'NA', 'stdAges': 'NA', 'eligibilityCriteria': 'NA'
        })
        return

    module = json_modules[module_key]

    raw_vars['healthyVolunteers'] = module.get('healthyVolunteers', 'NA')
    raw_vars['sex'] = module.get('sex', 'NA')
    raw_vars['minimumAge'] = module.get('minimumAge', 'NA')
    raw_vars['maximumAge'] = module.get('maximumAge', 'NA')
    raw_vars['eligibilityCriteria'] = module.get('eligibilityCriteria', 'NA')

    # Standard ages array
    std_ages = module.get('stdAges', [])
    if std_ages:
        raw_vars['stdAges_count'] = len(std_ages)
        raw_vars['stdAges'] = ','.join(std_ages)
    else:
        raw_vars['stdAges_count'] = 0
        raw_vars['stdAges'] = 'NA'


def extract_contacts_locations_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_contactsLocationsModule"""

    module_key = 'protocolSection_contactsLocationsModule'
    if module_key not in json_modules:
        raw_vars.update({
            'overallOfficials_count': 'NA', 'overallOfficials_roles': 'NA', 'overallOfficials_affiliations': 'NA',
            'locations_count': 'NA', 'locations_countries': 'NA', 'locations_cities': 'NA', 'locations_states': 'NA'
        })
        return

    module = json_modules[module_key]

    # Overall officials array
    officials = module.get('overallOfficials', [])
    if officials:
        raw_vars['overallOfficials_count'] = len(officials)
        raw_vars['overallOfficials_roles'] = ','.join([of.get('role', '') for of in officials])
        raw_vars['overallOfficials_affiliations'] = ','.join([of.get('affiliation', '') for of in officials])
    else:
        raw_vars['overallOfficials_count'] = 0
        raw_vars['overallOfficials_roles'] = 'NA'
        raw_vars['overallOfficials_affiliations'] = 'NA'

    # Locations array
    locations = module.get('locations', [])
    if locations:
        raw_vars['locations_count'] = len(locations)
        raw_vars['locations_countries'] = ','.join(list(set([loc.get('country', '') for loc in locations])))
        raw_vars['locations_cities'] = ','.join([loc.get('city', '') for loc in locations])
        raw_vars['locations_states'] = ','.join([loc.get('state', '') for loc in locations])
    else:
        raw_vars['locations_count'] = 0
        raw_vars['locations_countries'] = 'NA'
        raw_vars['locations_cities'] = 'NA'
        raw_vars['locations_states'] = 'NA'


def extract_outcomes_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_outcomesModule"""

    module_key = 'protocolSection_outcomesModule'
    if module_key not in json_modules:
        raw_vars.update({
            'primaryOutcomes_count': 'NA', 'primaryOutcomes_measures': 'NA', 'primaryOutcomes_timeFrames': 'NA',
            'secondaryOutcomes_count': 'NA', 'secondaryOutcomes_measures': 'NA', 'secondaryOutcomes_timeFrames': 'NA'
        })
        return

    module = json_modules[module_key]

    # Primary outcomes array
    primary_outcomes = module.get('primaryOutcomes', [])
    if primary_outcomes:
        raw_vars['primaryOutcomes_count'] = len(primary_outcomes)
        raw_vars['primaryOutcomes_measures'] = ','.join([po.get('measure', '') for po in primary_outcomes])
        raw_vars['primaryOutcomes_timeFrames'] = ','.join([po.get('timeFrame', '') for po in primary_outcomes])
    else:
        raw_vars['primaryOutcomes_count'] = 0
        raw_vars['primaryOutcomes_measures'] = 'NA'
        raw_vars['primaryOutcomes_timeFrames'] = 'NA'

    # Secondary outcomes array
    secondary_outcomes = module.get('secondaryOutcomes', [])
    if secondary_outcomes:
        raw_vars['secondaryOutcomes_count'] = len(secondary_outcomes)
        raw_vars['secondaryOutcomes_measures'] = ','.join([so.get('measure', '') for so in secondary_outcomes])
        raw_vars['secondaryOutcomes_timeFrames'] = ','.join([so.get('timeFrame', '') for so in secondary_outcomes])
    else:
        raw_vars['secondaryOutcomes_count'] = 0
        raw_vars['secondaryOutcomes_measures'] = 'NA'
        raw_vars['secondaryOutcomes_timeFrames'] = 'NA'


def extract_sponsor_collaborators_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_sponsorCollaboratorsModule"""

    module_key = 'protocolSection_sponsorCollaboratorsModule'
    if module_key not in json_modules:
        raw_vars.update({
            'responsibleParty_type': 'NA', 'leadSponsor_name': 'NA', 'leadSponsor_class': 'NA',
            'collaborators_count': 'NA', 'collaborators_names': 'NA', 'collaborators_classes': 'NA'
        })
        return

    module = json_modules[module_key]

    # Responsible party
    responsible_party = module.get('responsibleParty', {})
    raw_vars['responsibleParty_type'] = responsible_party.get('type', 'NA')

    # Lead sponsor
    lead_sponsor = module.get('leadSponsor', {})
    raw_vars['leadSponsor_name'] = lead_sponsor.get('name', 'NA')
    raw_vars['leadSponsor_class'] = lead_sponsor.get('class', 'NA')

    # Collaborators array
    collaborators = module.get('collaborators', [])
    if collaborators:
        raw_vars['collaborators_count'] = len(collaborators)
        raw_vars['collaborators_names'] = ','.join([col.get('name', '') for col in collaborators])
        raw_vars['collaborators_classes'] = ','.join([col.get('class', '') for col in collaborators])
    else:
        raw_vars['collaborators_count'] = 0
        raw_vars['collaborators_names'] = 'NA'
        raw_vars['collaborators_classes'] = 'NA'


def extract_oversight_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_oversightModule"""

    module_key = 'protocolSection_oversightModule'
    if module_key not in json_modules:
        raw_vars.update({
            'oversightHasDmc': 'NA', 'isFdaRegulatedDrug': 'NA', 'isFdaRegulatedDevice': 'NA', 'isUsExport': 'NA'
        })
        return

    module = json_modules[module_key]

    raw_vars['oversightHasDmc'] = module.get('oversightHasDmc', 'NA')
    raw_vars['isFdaRegulatedDrug'] = module.get('isFdaRegulatedDrug', 'NA')
    raw_vars['isFdaRegulatedDevice'] = module.get('isFdaRegulatedDevice', 'NA')
    raw_vars['isUsExport'] = module.get('isUsExport', 'NA')


def extract_description_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_descriptionModule"""

    module_key = 'protocolSection_descriptionModule'
    if module_key not in json_modules:
        raw_vars.update({
            'briefSummary': 'NA', 'detailedDescription': 'NA'
        })
        return

    module = json_modules[module_key]

    raw_vars['briefSummary'] = module.get('briefSummary', 'NA')
    raw_vars['detailedDescription'] = module.get('detailedDescription', 'NA')


def extract_references_module_vars(raw_vars, json_modules):
    """Extract variables from protocolSection_referencesModule"""

    module_key = 'protocolSection_referencesModule'
    if module_key not in json_modules:
        raw_vars.update({
            'references_count': 'NA', 'references_types': 'NA', 'availIpds_count': 'NA'
        })
        return

    module = json_modules[module_key]

    # References array
    references = module.get('references', [])
    if references:
        raw_vars['references_count'] = len(references)
        raw_vars['references_types'] = ','.join([ref.get('type', '') for ref in references])
    else:
        raw_vars['references_count'] = 0
        raw_vars['references_types'] = 'NA'

    # Available IPDs array
    avail_ipds = module.get('availIpds', [])
    raw_vars['availIpds_count'] = len(avail_ipds) if avail_ipds else 0

def extract_baseline_characteristics_module_vars(raw_vars, json_modules):
    """Extract variables from resultsSection_baselineCharacteristicsModule"""

    module_key = 'resultsSection_baselineCharacteristicsModule'
    if module_key not in json_modules:
        raw_vars.update({
            'baseline_groups_count': 'NA', 'baseline_denoms_units': 'NA', 'baseline_total_participants': 'NA',
            'baseline_measures_count': 'NA', 'baseline_age_mean': 'NA', 'baseline_age_sd': 'NA',
            'baseline_sex_male_count': 'NA', 'baseline_sex_female_count': 'NA'
        })
        return

    module = json_modules[module_key]

    # Groups array
    groups = module.get('groups', [])
    raw_vars['baseline_groups_count'] = len(groups) if groups else 0

    # Denominators - get total participants
    denoms = module.get('denoms', [])
    if denoms and len(denoms) > 0:
        raw_vars['baseline_denoms_units'] = denoms[0].get('units', 'NA')
        counts = denoms[0].get('counts', [])
        # Find total group (usually last one)
        total_count = 'NA'
        for count in counts:
            if count.get('groupId', '').endswith('007') or 'total' in count.get('groupId', '').lower():
                total_count = count.get('value', 'NA')
                break
        raw_vars['baseline_total_participants'] = total_count
    else:
        raw_vars['baseline_denoms_units'] = 'NA'
        raw_vars['baseline_total_participants'] = 'NA'

    # Measures - extract key demographic data
    measures = module.get('measures', [])
    raw_vars['baseline_measures_count'] = len(measures) if measures else 0

    # Extract age and sex data from measures
    raw_vars['baseline_age_mean'] = 'NA'
    raw_vars['baseline_age_sd'] = 'NA'
    raw_vars['baseline_sex_male_count'] = 'NA'
    raw_vars['baseline_sex_female_count'] = 'NA'

    for measure in measures:
        title = measure.get('title', '').lower()
        if 'age continuous' in title or 'age, continuous' in title:
            # Extract age data
            classes = measure.get('classes', [])
            if classes and len(classes) > 0:
                categories = classes[0].get('categories', [])
                if categories and len(categories) > 0:
                    measurements = categories[0].get('measurements', [])
                    # Find total group measurement
                    for measurement in measurements:
                        if measurement.get('groupId', '').endswith('007') or 'total' in measurement.get('groupId', '').lower():
                            raw_vars['baseline_age_mean'] = measurement.get('value', 'NA')
                            raw_vars['baseline_age_sd'] = measurement.get('spread', 'NA')
                            break

        elif 'sex' in title and ('female' in title or 'male' in title):
            # Extract sex data
            classes = measure.get('classes', [])
            if classes and len(classes) > 0:
                categories = classes[0].get('categories', [])
                for category in categories:
                    cat_title = category.get('title', '').lower()
                    measurements = category.get('measurements', [])
                    # Find total group measurement
                    for measurement in measurements:
                        if measurement.get('groupId', '').endswith('007') or 'total' in measurement.get('groupId', '').lower():
                            if 'male' in cat_title and 'female' not in cat_title:
                                raw_vars['baseline_sex_male_count'] = measurement.get('value', 'NA')
                            elif 'female' in cat_title:
                                raw_vars['baseline_sex_female_count'] = measurement.get('value', 'NA')
                            break


def extract_participant_flow_module_vars(raw_vars, json_modules):
    """Extract variables from resultsSection_participantFlowModule"""

    module_key = 'resultsSection_participantFlowModule'
    if module_key not in json_modules:
        raw_vars.update({
            'participantFlow_groups_count': 'NA', 'participantFlow_started_total': 'NA',
            'participantFlow_completed_total': 'NA', 'participantFlow_notCompleted_total': 'NA',
            'participantFlow_dropouts_adverseEvent': 'NA', 'participantFlow_dropouts_lackEfficacy': 'NA',
            'participantFlow_dropouts_lostFollowup': 'NA', 'participantFlow_dropouts_withdrawal': 'NA'
        })
        return

    module = json_modules[module_key]

    # Groups
    groups = module.get('groups', [])
    raw_vars['participantFlow_groups_count'] = len(groups) if groups else 0

    # Periods and milestones
    periods = module.get('periods', [])
    if periods and len(periods) > 0:
        milestones = periods[0].get('milestones', [])

        # Extract milestone data
        for milestone in milestones:
            milestone_type = milestone.get('type', '')
            achievements = milestone.get('achievements', [])
            total_subjects = sum(int(ach.get('numSubjects', 0)) for ach in achievements if ach.get('numSubjects', '').isdigit())

            if milestone_type == 'STARTED':
                raw_vars['participantFlow_started_total'] = total_subjects
            elif milestone_type == 'COMPLETED':
                raw_vars['participantFlow_completed_total'] = total_subjects
            elif milestone_type == 'NOT COMPLETED':
                raw_vars['participantFlow_notCompleted_total'] = total_subjects

        # Extract dropout reasons
        drop_withdraws = periods[0].get('dropWithdraws', [])
        raw_vars['participantFlow_dropouts_adverseEvent'] = 0
        raw_vars['participantFlow_dropouts_lackEfficacy'] = 0
        raw_vars['participantFlow_dropouts_lostFollowup'] = 0
        raw_vars['participantFlow_dropouts_withdrawal'] = 0

        for dropout in drop_withdraws:
            dropout_type = dropout.get('type', '').lower()
            reasons = dropout.get('reasons', [])
            total_dropouts = sum(int(reason.get('numSubjects', 0)) for reason in reasons if reason.get('numSubjects', '').isdigit())

            if 'adverse' in dropout_type:
                raw_vars['participantFlow_dropouts_adverseEvent'] = total_dropouts
            elif 'efficacy' in dropout_type:
                raw_vars['participantFlow_dropouts_lackEfficacy'] = total_dropouts
            elif 'follow' in dropout_type:
                raw_vars['participantFlow_dropouts_lostFollowup'] = total_dropouts
            elif 'withdrawal' in dropout_type:
                raw_vars['participantFlow_dropouts_withdrawal'] = total_dropouts


def extract_outcome_measures_module_vars(raw_vars, json_modules):
    """Extract variables from resultsSection_outcomeMeasuresModule - General Template"""

    module_key = 'resultsSection_outcomeMeasuresModule'
    if module_key not in json_modules:
        raw_vars.update({
            'outcomeMeasures_count': 'NA', 'outcomeMeasures_primary_count': 'NA', 'outcomeMeasures_secondary_count': 'NA',
            'outcomeMeasures_titles': 'NA', 'outcomeMeasures_paramTypes': 'NA', 'outcomeMeasures_unitsOfMeasure': 'NA'
        })
        return

    module = json_modules[module_key]

    # Outcome measures array - general template approach
    outcome_measures = module.get('outcomeMeasures', [])
    if outcome_measures:
        raw_vars['outcomeMeasures_count'] = len(outcome_measures)

        # Count by type
        primary_count = sum(1 for om in outcome_measures if om.get('type') == 'PRIMARY')
        secondary_count = sum(1 for om in outcome_measures if om.get('type') == 'SECONDARY')
        raw_vars['outcomeMeasures_primary_count'] = primary_count
        raw_vars['outcomeMeasures_secondary_count'] = secondary_count

        # Extract general information
        raw_vars['outcomeMeasures_titles'] = ','.join([om.get('title', '') for om in outcome_measures])
        raw_vars['outcomeMeasures_paramTypes'] = ','.join([om.get('paramType', '') for om in outcome_measures])
        raw_vars['outcomeMeasures_unitsOfMeasure'] = ','.join([om.get('unitOfMeasure', '') for om in outcome_measures])
    else:
        raw_vars['outcomeMeasures_count'] = 0
        raw_vars['outcomeMeasures_primary_count'] = 0
        raw_vars['outcomeMeasures_secondary_count'] = 0
        raw_vars['outcomeMeasures_titles'] = 'NA'
        raw_vars['outcomeMeasures_paramTypes'] = 'NA'
        raw_vars['outcomeMeasures_unitsOfMeasure'] = 'NA'


def extract_adverse_events_module_vars(raw_vars, json_modules):
    """Extract variables from resultsSection_adverseEventsModule"""

    module_key = 'resultsSection_adverseEventsModule'
    if module_key not in json_modules:
        raw_vars.update({
            'adverseEvents_frequencyThreshold': 'NA', 'adverseEvents_timeFrame': 'NA',
            'adverseEvents_groups_count': 'NA', 'adverseEvents_seriousEvents_count': 'NA', 'adverseEvents_otherEvents_count': 'NA'
        })
        return

    module = json_modules[module_key]

    raw_vars['adverseEvents_frequencyThreshold'] = module.get('frequencyThreshold', 'NA')
    raw_vars['adverseEvents_timeFrame'] = module.get('timeFrame', 'NA')

    # Event groups
    event_groups = module.get('eventGroups', [])
    raw_vars['adverseEvents_groups_count'] = len(event_groups) if event_groups else 0

    # Serious events
    serious_events = module.get('seriousEvents', [])
    raw_vars['adverseEvents_seriousEvents_count'] = len(serious_events) if serious_events else 0

    # Other events
    other_events = module.get('otherEvents', [])
    raw_vars['adverseEvents_otherEvents_count'] = len(other_events) if other_events else 0


def extract_more_info_module_vars(raw_vars, json_modules):
    """Extract variables from resultsSection_moreInfoModule and related modules"""

    # More info module
    more_info_key = 'resultsSection_moreInfoModule'
    if more_info_key in json_modules:
        module = json_modules[more_info_key]
        raw_vars['moreInfo_available'] = 'YES'
    else:
        raw_vars['moreInfo_available'] = 'NA'

    # Certain agreements module
    agreements_key = 'resultsSection_certainAgreementsModule'
    if agreements_key in json_modules:
        raw_vars['certainAgreements_available'] = 'YES'
    else:
        raw_vars['certainAgreements_available'] = 'NA'

    # Point of contact module
    contact_key = 'resultsSection_pointOfContactModule'
    if contact_key in json_modules:
        raw_vars['pointOfContact_available'] = 'YES'
    else:
        raw_vars['pointOfContact_available'] = 'NA'

    # Limitations and caveats module
    limitations_key = 'resultsSection_limitationsAndCaveatsModule'
    if limitations_key in json_modules:
        raw_vars['limitationsAndCaveats_available'] = 'YES'
    else:
        raw_vars['limitationsAndCaveats_available'] = 'NA'


def extract_derived_section_vars(raw_vars, json_modules):
    """Extract variables from derivedSection modules"""

    # Derived section
    derived_key = 'derivedSection_derivedSection'
    if derived_key in json_modules:
        raw_vars['derivedSection_available'] = 'YES'
    else:
        raw_vars['derivedSection_available'] = 'NA'

    # Document section
    document_key = 'derivedSection_documentSection'
    if document_key in json_modules:
        raw_vars['documentSection_available'] = 'YES'
    else:
        raw_vars['documentSection_available'] = 'NA'

    # Misc info module
    misc_key = 'derivedSection_miscInfoModule'
    if misc_key in json_modules:
        module = json_modules[misc_key]
        raw_vars['miscInfo_versionHolder'] = module.get('versionHolder', 'NA')
        removed_countries = module.get('removedCountries', [])
        if removed_countries:
            raw_vars['miscInfo_removedCountries'] = ','.join(removed_countries)
            raw_vars['miscInfo_removedCountries_count'] = len(removed_countries)
        else:
            raw_vars['miscInfo_removedCountries'] = 'NA'
            raw_vars['miscInfo_removedCountries_count'] = 0
    else:
        raw_vars['miscInfo_versionHolder'] = 'NA'
        raw_vars['miscInfo_removedCountries'] = 'NA'
        raw_vars['miscInfo_removedCountries_count'] = 'NA'


def write_raw_variables_csv(raw_variables_data, csv_file_path):
    """
    Write raw variables data to CSV file with columns ordered as documented in README

    Args:
        raw_variables_data (list): List of dictionaries containing raw variables for each study
        csv_file_path (str): Path where CSV file should be saved
    """

    if not raw_variables_data:
        print("⚠️ No raw variables data to write")
        return

    # Define column order exactly as documented in README
    ordered_columns = [
        # Study identifier (always first)
        'study_id',

        # 1. protocolSection_identificationModule
        'nctId',
        'orgStudyIdInfo_id',
        'secondaryIdInfos_count',
        'secondaryIdInfos_types',
        'secondaryIdInfos_domains',
        'organization_fullName',
        'organization_class',
        'briefTitle',
        'officialTitle',

        # 2. protocolSection_statusModule
        'statusVerifiedDate',
        'overallStatus',
        'expandedAccessInfo_hasExpandedAccess',
        'startDateStruct_date',
        'startDateStruct_type',
        'primaryCompletionDateStruct_date',
        'primaryCompletionDateStruct_type',
        'completionDateStruct_date',
        'completionDateStruct_type',
        'studyFirstSubmitDate',
        'studyFirstSubmitQcDate',
        'studyFirstPostDateStruct_date',
        'studyFirstPostDateStruct_type',
        'resultsFirstSubmitDate',
        'resultsFirstSubmitQcDate',
        'resultsFirstPostDateStruct_date',
        'resultsFirstPostDateStruct_type',
        'lastUpdateSubmitDate',
        'lastUpdatePostDateStruct_date',
        'lastUpdatePostDateStruct_type',

        # 3. protocolSection_designModule
        'studyType',
        'phases',
        'phases_count',
        'designInfo_allocation',
        'designInfo_interventionModel',
        'designInfo_primaryPurpose',
        'designInfo_maskingInfo_masking',
        'designInfo_maskingInfo_whoMasked',
        'designInfo_maskingInfo_whoMasked_count',
        'enrollmentInfo_count',
        'enrollmentInfo_type',

        # 4. protocolSection_armsInterventionsModule
        'armGroups_count',
        'armGroups_types',
        'armGroups_labels',
        'interventions_count',
        'interventions_types',
        'interventions_names',

        # 5. protocolSection_conditionsModule
        'conditions_count',
        'conditions',
        'keywords_count',
        'keywords',

        # 6. protocolSection_eligibilityModule
        'healthyVolunteers',
        'sex',
        'minimumAge',
        'maximumAge',
        'stdAges_count',
        'stdAges',
        'eligibilityCriteria',

        # 7. protocolSection_contactsLocationsModule
        'overallOfficials_count',
        'overallOfficials_roles',
        'overallOfficials_affiliations',
        'locations_count',
        'locations_countries',
        'locations_cities',
        'locations_states',

        # 8. protocolSection_outcomesModule
        'primaryOutcomes_count',
        'primaryOutcomes_measures',
        'primaryOutcomes_timeFrames',
        'secondaryOutcomes_count',
        'secondaryOutcomes_measures',
        'secondaryOutcomes_timeFrames',

        # 9. protocolSection_sponsorCollaboratorsModule
        'responsibleParty_type',
        'leadSponsor_name',
        'leadSponsor_class',
        'collaborators_count',
        'collaborators_names',
        'collaborators_classes',

        # 10. protocolSection_oversightModule
        'oversightHasDmc',
        'isFdaRegulatedDrug',
        'isFdaRegulatedDevice',
        'isUsExport',

        # 11. protocolSection_descriptionModule
        'briefSummary',
        'detailedDescription',

        # 12. protocolSection_referencesModule
        'references_count',
        'references_types',
        'availIpds_count',

        # 13. resultsSection_baselineCharacteristicsModule
        'baseline_groups_count',
        'baseline_denoms_units',
        'baseline_total_participants',
        'baseline_measures_count',
        'baseline_age_mean',
        'baseline_age_sd',
        'baseline_sex_male_count',
        'baseline_sex_female_count',

        # 14. resultsSection_participantFlowModule
        'participantFlow_groups_count',
        'participantFlow_started_total',
        'participantFlow_completed_total',
        'participantFlow_notCompleted_total',
        'participantFlow_dropouts_adverseEvent',
        'participantFlow_dropouts_lackEfficacy',
        'participantFlow_dropouts_lostFollowup',
        'participantFlow_dropouts_withdrawal',

        # 15. resultsSection_outcomeMeasuresModule
        'outcomeMeasures_count',
        'outcomeMeasures_primary_count',
        'outcomeMeasures_secondary_count',
        'outcomeMeasures_titles',
        'outcomeMeasures_paramTypes',
        'outcomeMeasures_unitsOfMeasure',

        # 16. resultsSection_adverseEventsModule
        'adverseEvents_frequencyThreshold',
        'adverseEvents_timeFrame',
        'adverseEvents_groups_count',
        'adverseEvents_seriousEvents_count',
        'adverseEvents_otherEvents_count',

        # 17. resultsSection_moreInfoModule and related
        'moreInfo_available',
        'certainAgreements_available',
        'pointOfContact_available',
        'limitationsAndCaveats_available',

        # 18. derivedSection modules
        'derivedSection_available',
        'documentSection_available',
        'miscInfo_versionHolder',
        'miscInfo_removedCountries',
        'miscInfo_removedCountries_count'
    ]

    # Get all actual columns from data to catch any we might have missed
    all_actual_columns = set()
    for study_data in raw_variables_data:
        all_actual_columns.update(study_data.keys())

    # Add any columns that exist in data but not in our ordered list (shouldn't happen, but safety check)
    missing_columns = all_actual_columns - set(ordered_columns)
    if missing_columns:
        print(f"⚠️ Found unexpected columns not in ordered list: {sorted(missing_columns)}")
        ordered_columns.extend(sorted(missing_columns))

    # Write CSV file with ordered columns
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=ordered_columns)
        writer.writeheader()

        for study_data in raw_variables_data:
            # Fill missing columns with 'NA'
            complete_row = {col: study_data.get(col, 'NA') for col in ordered_columns}
            writer.writerow(complete_row)

    print(f"📊 Raw variables CSV written with {len(raw_variables_data)} studies and {len(ordered_columns)} variables")
    print(f"📋 Columns ordered as documented in README (modules 1-18)")


def generate_raw_variables_table1(studies_dir):
    """
    Generate Table 1 raw variables CSV from studies directory

    Args:
        studies_dir (str): Path to directory containing NCT study folders

    Returns:
        dict: Success status and file path information
    """

    try:
        print("📊 Generating Table 1: Raw Variables from JSON Modules")

        # Get all NCT study directories
        study_dirs = [d for d in os.listdir(studies_dir)
                     if os.path.isdir(os.path.join(studies_dir, d)) and d.startswith('NCT')]

        if not study_dirs:
            return {'success': False, 'error': 'No NCT study directories found'}

        print(f"📁 Found {len(study_dirs)} studies to process")

        # Extract raw variables from each study
        raw_variables_data = []
        for i, study_id in enumerate(study_dirs, 1):
            print(f"🔍 Processing {study_id} ({i}/{len(study_dirs)}) - Raw variables")

            study_path = os.path.join(studies_dir, study_id)
            raw_variables = extract_raw_variables_from_json(study_path, study_id)
            raw_variables_data.append(raw_variables)

        # Determine output directory
        output_dir = os.path.dirname(studies_dir)
        if 'output_meta' in output_dir:
            # Extract project name from path like output_meta/diabetes/canagliflozin
            path_parts = output_dir.split(os.sep)
            if len(path_parts) >= 2:
                project_name = path_parts[-2] if path_parts[-2] != 'output_meta' else path_parts[-1]
                csv_output_dir = os.path.join('output_meta', project_name, 'R_output')
            else:
                csv_output_dir = os.path.join(output_dir, 'R_output')
        else:
            csv_output_dir = os.path.join(output_dir, 'meta_analysis_output')

        os.makedirs(csv_output_dir, exist_ok=True)

        # Generate raw variables CSV
        raw_csv_path = os.path.join(csv_output_dir, 'table1_raw_variables.csv')
        write_raw_variables_csv(raw_variables_data, raw_csv_path)

        return {
            'success': True,
            'raw_csv_file': raw_csv_path,
            'studies_processed': len(study_dirs),
            'message': f'Table 1 raw variables generated with {len(study_dirs)} studies'
        }

    except Exception as e:
        print(f"❌ Error generating raw variables Table 1: {e}")
        return {'success': False, 'error': str(e)}


# =============================================================================
# META-VARIABLES GENERATION FUNCTIONS
# =============================================================================

def generate_meta_variables_from_raw(raw_variables_data):
    """
    Generate meta-variables from raw variables data

    Args:
        raw_variables_data (list): List of dictionaries containing raw variables for each study

    Returns:
        list: List of dictionaries containing meta-variables for each study
    """

    meta_variables_data = []

    for raw_vars in raw_variables_data:
        meta_vars = {'study_id': raw_vars.get('study_id', 'NA')}

        # Generate all meta-variables
        generate_temporal_meta_variables(meta_vars, raw_vars)
        generate_demographic_meta_variables(meta_vars, raw_vars)
        generate_design_meta_variables(meta_vars, raw_vars)
        generate_participant_flow_meta_variables(meta_vars, raw_vars)
        generate_geographic_regulatory_meta_variables(meta_vars, raw_vars)
        generate_sponsorship_meta_variables(meta_vars, raw_vars)
        generate_outcome_meta_variables(meta_vars, raw_vars)
        generate_safety_meta_variables(meta_vars, raw_vars)
        generate_data_quality_meta_variables(meta_vars, raw_vars)
        generate_subgroup_meta_variables(meta_vars, raw_vars)

        meta_variables_data.append(meta_vars)

    return meta_variables_data


def safe_float_convert(value, default_na=True):
    """Safely convert value to float, return NA if conversion fails"""
    if value == 'NA' or value is None or value == '':
        return 'NA'
    try:
        return float(value)
    except (ValueError, TypeError):
        return 'NA' if default_na else 0.0


def safe_int_convert(value, default_na=True):
    """Safely convert value to int, return NA if conversion fails"""
    if value == 'NA' or value is None or value == '':
        return 'NA'
    try:
        return int(float(value))  # Handle cases like "123.0"
    except (ValueError, TypeError):
        return 'NA' if default_na else 0


def calculate_date_difference_months(start_date, end_date):
    """Calculate difference between two dates in months"""
    if start_date == 'NA' or end_date == 'NA' or not start_date or not end_date:
        return 'NA'

    try:
        from datetime import datetime
        # Handle various date formats
        date_formats = ['%Y-%m-%d', '%Y-%m', '%Y']

        start_dt = None
        end_dt = None

        for fmt in date_formats:
            try:
                start_dt = datetime.strptime(str(start_date), fmt)
                break
            except ValueError:
                continue

        for fmt in date_formats:
            try:
                end_dt = datetime.strptime(str(end_date), fmt)
                break
            except ValueError:
                continue

        if start_dt and end_dt:
            # Calculate difference in months
            months_diff = (end_dt.year - start_dt.year) * 12 + (end_dt.month - start_dt.month)
            return months_diff
        else:
            return 'NA'

    except Exception:
        return 'NA'


def generate_temporal_meta_variables(meta_vars, raw_vars):
    """Generate temporal meta-variables (A. TEMPORAL META-VARIABLES)"""

    # 1. study_duration_months
    meta_vars['study_duration_months'] = calculate_date_difference_months(
        raw_vars.get('startDateStruct_date'),
        raw_vars.get('completionDateStruct_date')
    )

    # 2. primary_completion_duration_months
    meta_vars['primary_completion_duration_months'] = calculate_date_difference_months(
        raw_vars.get('startDateStruct_date'),
        raw_vars.get('primaryCompletionDateStruct_date')
    )

    # 3. time_to_results_posting_months
    meta_vars['time_to_results_posting_months'] = calculate_date_difference_months(
        raw_vars.get('completionDateStruct_date'),
        raw_vars.get('resultsFirstPostDateStruct_date')
    )

    # 4. regulatory_review_time_months
    meta_vars['regulatory_review_time_months'] = calculate_date_difference_months(
        raw_vars.get('resultsFirstSubmitDate'),
        raw_vars.get('resultsFirstPostDateStruct_date')
    )


def generate_demographic_meta_variables(meta_vars, raw_vars):
    """Generate demographic meta-variables (B. DEMOGRAPHIC META-VARIABLES)"""

    # 5. baseline_age_mean_numeric
    meta_vars['baseline_age_mean_numeric'] = safe_float_convert(raw_vars.get('baseline_age_mean'))

    # 6. baseline_age_sd_numeric
    meta_vars['baseline_age_sd_numeric'] = safe_float_convert(raw_vars.get('baseline_age_sd'))

    # 7. male_percentage
    male_count = safe_int_convert(raw_vars.get('baseline_sex_male_count'), default_na=False)
    total_participants = safe_int_convert(raw_vars.get('baseline_total_participants'), default_na=False)

    if male_count != 'NA' and total_participants != 'NA' and total_participants > 0:
        meta_vars['male_percentage'] = round((male_count / total_participants) * 100, 2)
    else:
        meta_vars['male_percentage'] = 'NA'

    # 8. female_percentage
    female_count = safe_int_convert(raw_vars.get('baseline_sex_female_count'), default_na=False)

    if female_count != 'NA' and total_participants != 'NA' and total_participants > 0:
        meta_vars['female_percentage'] = round((female_count / total_participants) * 100, 2)
    else:
        meta_vars['female_percentage'] = 'NA'

    # 9. age_category - THRESHOLD: Young (<50), Middle (50-65), Older (>65)
    age_mean = meta_vars['baseline_age_mean_numeric']
    if age_mean != 'NA':
        if age_mean < 50:
            meta_vars['age_category'] = 'Young'
        elif age_mean <= 65:
            meta_vars['age_category'] = 'Middle'
        else:
            meta_vars['age_category'] = 'Older'
    else:
        meta_vars['age_category'] = 'NA'

    # 10. sample_size_category - THRESHOLD: Small (<100), Medium (100-500), Large (>500)
    if total_participants != 'NA':
        if total_participants < 100:
            meta_vars['sample_size_category'] = 'Small'
        elif total_participants <= 500:
            meta_vars['sample_size_category'] = 'Medium'
        else:
            meta_vars['sample_size_category'] = 'Large'
    else:
        meta_vars['sample_size_category'] = 'NA'

    # 11. gender_balance_category - THRESHOLD: Male-dominated (>60%), Balanced (40-60%), Female-dominated (<40%)
    male_pct = meta_vars['male_percentage']
    if male_pct != 'NA':
        if male_pct > 60:
            meta_vars['gender_balance_category'] = 'Male-dominated'
        elif male_pct >= 40:
            meta_vars['gender_balance_category'] = 'Balanced'
        else:
            meta_vars['gender_balance_category'] = 'Female-dominated'
    else:
        meta_vars['gender_balance_category'] = 'NA'


def generate_design_meta_variables(meta_vars, raw_vars):
    """Generate study design meta-variables (C. STUDY DESIGN META-VARIABLES)"""

    # 12. design_complexity_score
    arm_groups = safe_int_convert(raw_vars.get('armGroups_count'), default_na=False)
    interventions = safe_int_convert(raw_vars.get('interventions_count'), default_na=False)
    outcomes = safe_int_convert(raw_vars.get('outcomeMeasures_count'), default_na=False)

    if arm_groups != 'NA' or interventions != 'NA' or outcomes != 'NA':
        score = (arm_groups if arm_groups != 'NA' else 0) + \
                (interventions if interventions != 'NA' else 0) + \
                (outcomes if outcomes != 'NA' else 0)
        meta_vars['design_complexity_score'] = score
    else:
        meta_vars['design_complexity_score'] = 'NA'

    # 13. intervention_complexity_score
    if interventions != 'NA':
        # Base score on number of interventions, could be enhanced with intervention types
        meta_vars['intervention_complexity_score'] = interventions
    else:
        meta_vars['intervention_complexity_score'] = 'NA'

    # 14. outcome_complexity_score
    primary_outcomes = safe_int_convert(raw_vars.get('primaryOutcomes_count'), default_na=False)
    secondary_outcomes = safe_int_convert(raw_vars.get('secondaryOutcomes_count'), default_na=False)

    if primary_outcomes != 'NA' or secondary_outcomes != 'NA':
        primary_score = primary_outcomes if primary_outcomes != 'NA' else 0
        secondary_score = (secondary_outcomes * 0.5) if secondary_outcomes != 'NA' else 0
        meta_vars['outcome_complexity_score'] = round(primary_score + secondary_score, 2)
    else:
        meta_vars['outcome_complexity_score'] = 'NA'

    # 15. masking_quality_category - Separate Triple and Quadruple
    masking = raw_vars.get('designInfo_maskingInfo_masking', 'NA')
    if masking != 'NA':
        masking_lower = str(masking).lower()
        if 'quadruple' in masking_lower:
            meta_vars['masking_quality_category'] = 'Quadruple'
        elif 'triple' in masking_lower:
            meta_vars['masking_quality_category'] = 'Triple'
        elif 'double' in masking_lower:
            meta_vars['masking_quality_category'] = 'Double'
        elif 'single' in masking_lower:
            meta_vars['masking_quality_category'] = 'Single'
        else:
            meta_vars['masking_quality_category'] = 'None'
    else:
        meta_vars['masking_quality_category'] = 'NA'

    # 16. allocation_method
    allocation = raw_vars.get('designInfo_allocation', 'NA')
    if allocation != 'NA':
        meta_vars['allocation_method'] = 'Randomized' if 'RANDOMIZED' in str(allocation).upper() else 'Non-randomized'
    else:
        meta_vars['allocation_method'] = 'NA'

    # 17. study_phase_category - Show actual phase numbers
    phases = raw_vars.get('phases', 'NA')
    if phases != 'NA' and phases:
        phases_str = str(phases).upper()
        # Check for multiple phases first
        if ('PHASE2' in phases_str or 'PHASE 2' in phases_str) and ('PHASE3' in phases_str or 'PHASE 3' in phases_str):
            meta_vars['study_phase_category'] = 'Phase 2/3'
        elif ('PHASE1' in phases_str or 'PHASE 1' in phases_str) and ('PHASE2' in phases_str or 'PHASE 2' in phases_str):
            meta_vars['study_phase_category'] = 'Phase 1/2'
        elif ('PHASE3' in phases_str or 'PHASE 3' in phases_str) and ('PHASE4' in phases_str or 'PHASE 4' in phases_str):
            meta_vars['study_phase_category'] = 'Phase 3/4'
        # Single phases
        elif 'PHASE4' in phases_str or 'PHASE 4' in phases_str:
            meta_vars['study_phase_category'] = 'Phase 4'
        elif 'PHASE3' in phases_str or 'PHASE 3' in phases_str:
            meta_vars['study_phase_category'] = 'Phase 3'
        elif 'PHASE2' in phases_str or 'PHASE 2' in phases_str:
            meta_vars['study_phase_category'] = 'Phase 2'
        elif 'PHASE1' in phases_str or 'PHASE 1' in phases_str:
            meta_vars['study_phase_category'] = 'Phase 1'
        else:
            meta_vars['study_phase_category'] = phases_str  # Keep original if unclear
    else:
        meta_vars['study_phase_category'] = 'NA'

    # 18. intervention_model_category
    model = raw_vars.get('designInfo_interventionModel', 'NA')
    meta_vars['intervention_model_category'] = model if model != 'NA' else 'NA'

    # 19. primary_purpose_category
    purpose = raw_vars.get('designInfo_primaryPurpose', 'NA')
    meta_vars['primary_purpose_category'] = purpose if purpose != 'NA' else 'NA'


def generate_participant_flow_meta_variables(meta_vars, raw_vars):
    """Generate participant flow meta-variables (D. PARTICIPANT FLOW META-VARIABLES)"""

    started = safe_int_convert(raw_vars.get('participantFlow_started_total'), default_na=False)
    completed = safe_int_convert(raw_vars.get('participantFlow_completed_total'), default_na=False)
    not_completed = safe_int_convert(raw_vars.get('participantFlow_notCompleted_total'), default_na=False)
    ae_dropouts = safe_int_convert(raw_vars.get('participantFlow_dropouts_adverseEvent'), default_na=False)
    efficacy_dropouts = safe_int_convert(raw_vars.get('participantFlow_dropouts_lackEfficacy'), default_na=False)
    lost_followup = safe_int_convert(raw_vars.get('participantFlow_dropouts_lostFollowup'), default_na=False)

    # 20. completion_rate
    if started != 'NA' and completed != 'NA' and started > 0:
        meta_vars['completion_rate'] = round((completed / started) * 100, 2)
    else:
        meta_vars['completion_rate'] = 'NA'

    # 21. dropout_rate
    if started != 'NA' and not_completed != 'NA' and started > 0:
        meta_vars['dropout_rate'] = round((not_completed / started) * 100, 2)
    else:
        meta_vars['dropout_rate'] = 'NA'

    # 22. adverse_event_dropout_rate
    if started != 'NA' and ae_dropouts != 'NA' and started > 0:
        meta_vars['adverse_event_dropout_rate'] = round((ae_dropouts / started) * 100, 2)
    else:
        meta_vars['adverse_event_dropout_rate'] = 'NA'

    # 23. efficacy_dropout_rate
    if started != 'NA' and efficacy_dropouts != 'NA' and started > 0:
        meta_vars['efficacy_dropout_rate'] = round((efficacy_dropouts / started) * 100, 2)
    else:
        meta_vars['efficacy_dropout_rate'] = 'NA'

    # 24. lost_followup_rate
    if started != 'NA' and lost_followup != 'NA' and started > 0:
        meta_vars['lost_followup_rate'] = round((lost_followup / started) * 100, 2)
    else:
        meta_vars['lost_followup_rate'] = 'NA'

    # 25. completion_rate_category - THRESHOLD: High (>90%), Medium (80-90%), Low (<80%)
    completion_rate = meta_vars['completion_rate']
    if completion_rate != 'NA':
        if completion_rate > 90:
            meta_vars['completion_rate_category'] = 'High'
        elif completion_rate >= 80:
            meta_vars['completion_rate_category'] = 'Medium'
        else:
            meta_vars['completion_rate_category'] = 'Low'
    else:
        meta_vars['completion_rate_category'] = 'NA'

    # 26. dropout_pattern_category
    ae_rate = meta_vars['adverse_event_dropout_rate']
    eff_rate = meta_vars['efficacy_dropout_rate']

    if ae_rate != 'NA' and eff_rate != 'NA':
        if ae_rate > eff_rate and ae_rate > 5:  # Threshold: >5% for AE-driven
            meta_vars['dropout_pattern_category'] = 'AE-driven'
        elif eff_rate > ae_rate and eff_rate > 5:  # Threshold: >5% for Efficacy-driven
            meta_vars['dropout_pattern_category'] = 'Efficacy-driven'
        elif ae_rate > 2 and eff_rate > 2:  # Both significant
            meta_vars['dropout_pattern_category'] = 'Mixed'
        else:
            meta_vars['dropout_pattern_category'] = 'Other'
    else:
        meta_vars['dropout_pattern_category'] = 'NA'


def generate_geographic_regulatory_meta_variables(meta_vars, raw_vars):
    """Generate geographic and regulatory meta-variables (E. GEOGRAPHIC AND REGULATORY META-VARIABLES)"""

    # 27. number_of_countries
    countries_str = raw_vars.get('locations_countries', 'NA')
    if countries_str != 'NA' and countries_str:
        # Count unique countries (comma-separated)
        countries = [c.strip() for c in str(countries_str).split(',') if c.strip()]
        meta_vars['number_of_countries'] = len(set(countries))
    else:
        meta_vars['number_of_countries'] = 'NA'

    # 28. number_of_locations
    meta_vars['number_of_locations'] = safe_int_convert(raw_vars.get('locations_count'))

    # 29. locations_per_country
    num_countries = meta_vars['number_of_countries']
    num_locations = meta_vars['number_of_locations']
    if num_countries != 'NA' and num_locations != 'NA' and num_countries > 0:
        meta_vars['locations_per_country'] = round(num_locations / num_countries, 2)
    else:
        meta_vars['locations_per_country'] = 'NA'

    # 30. geographic_scope_category - THRESHOLD: Single-country, Regional (2-5 countries), Global (>5 countries)
    if num_countries != 'NA':
        if num_countries == 1:
            meta_vars['geographic_scope_category'] = 'Single-country'
        elif num_countries <= 5:
            meta_vars['geographic_scope_category'] = 'Regional'
        else:
            meta_vars['geographic_scope_category'] = 'Global'
    else:
        meta_vars['geographic_scope_category'] = 'NA'

    # 31. regulatory_region_category
    if countries_str != 'NA' and countries_str:
        countries_lower = str(countries_str).lower()
        us_present = 'united states' in countries_lower
        eu_countries = ['germany', 'france', 'italy', 'spain', 'netherlands', 'belgium', 'poland', 'czechia', 'slovakia', 'hungary']
        eu_present = any(country in countries_lower for country in eu_countries)
        asia_countries = ['japan', 'korea', 'china', 'taiwan', 'singapore', 'thailand', 'malaysia']
        asia_present = any(country in countries_lower for country in asia_countries)

        if us_present and not eu_present and not asia_present:
            meta_vars['regulatory_region_category'] = 'US-only'
        elif eu_present and not us_present and not asia_present:
            meta_vars['regulatory_region_category'] = 'EU-only'
        elif asia_present and not us_present and not eu_present:
            meta_vars['regulatory_region_category'] = 'Asia-Pacific'
        else:
            meta_vars['regulatory_region_category'] = 'Global'
    else:
        meta_vars['regulatory_region_category'] = 'NA'

    # 32. fda_regulation_category
    fda_drug = raw_vars.get('isFdaRegulatedDrug', 'NA')
    fda_device = raw_vars.get('isFdaRegulatedDevice', 'NA')

    if fda_drug != 'NA' or fda_device != 'NA':
        drug_regulated = str(fda_drug).lower() == 'true'
        device_regulated = str(fda_device).lower() == 'true'

        if drug_regulated and device_regulated:
            meta_vars['fda_regulation_category'] = 'Both'
        elif drug_regulated:
            meta_vars['fda_regulation_category'] = 'Drug'
        elif device_regulated:
            meta_vars['fda_regulation_category'] = 'Device'
        else:
            meta_vars['fda_regulation_category'] = 'None'
    else:
        meta_vars['fda_regulation_category'] = 'NA'

    # 33. regulatory_oversight_category
    has_dmc = raw_vars.get('oversightHasDmc', 'NA')
    if has_dmc != 'NA':
        meta_vars['regulatory_oversight_category'] = 'With DMC' if str(has_dmc).lower() == 'true' else 'Without DMC'
    else:
        meta_vars['regulatory_oversight_category'] = 'NA'


def generate_sponsorship_meta_variables(meta_vars, raw_vars):
    """Generate sponsorship and funding meta-variables (F. SPONSORSHIP AND FUNDING META-VARIABLES)"""

    # 34. sponsor_type_category
    sponsor_class = raw_vars.get('leadSponsor_class', 'NA')
    if sponsor_class != 'NA':
        sponsor_upper = str(sponsor_class).upper()
        if 'INDUSTRY' in sponsor_upper:
            meta_vars['sponsor_type_category'] = 'Industry'
        elif 'ACADEMIC' in sponsor_upper or 'UNIVERSITY' in sponsor_upper:
            meta_vars['sponsor_type_category'] = 'Academic'
        elif 'GOVERNMENT' in sponsor_upper or 'NIH' in sponsor_upper:
            meta_vars['sponsor_type_category'] = 'Government'
        else:
            meta_vars['sponsor_type_category'] = 'Other'
    else:
        meta_vars['sponsor_type_category'] = 'NA'

    # 35. collaboration_level_category - THRESHOLD: Solo (0), Limited (1-2), Extensive (>2)
    collaborators_count = safe_int_convert(raw_vars.get('collaborators_count'), default_na=False)
    if collaborators_count != 'NA':
        if collaborators_count == 0:
            meta_vars['collaboration_level_category'] = 'Solo'
        elif collaborators_count <= 2:
            meta_vars['collaboration_level_category'] = 'Limited'
        else:
            meta_vars['collaboration_level_category'] = 'Extensive'
    else:
        meta_vars['collaboration_level_category'] = 'NA'

    # 36. total_sponsor_entities
    if collaborators_count != 'NA':
        meta_vars['total_sponsor_entities'] = 1 + collaborators_count
    else:
        meta_vars['total_sponsor_entities'] = 'NA'


def generate_outcome_meta_variables(meta_vars, raw_vars):
    """Generate outcome and efficacy meta-variables (G. OUTCOME AND EFFICACY META-VARIABLES)"""

    primary_count = safe_int_convert(raw_vars.get('primaryOutcomes_count'), default_na=False)
    secondary_count = safe_int_convert(raw_vars.get('secondaryOutcomes_count'), default_na=False)

    # 37. total_outcome_measures
    if primary_count != 'NA' or secondary_count != 'NA':
        primary = primary_count if primary_count != 'NA' else 0
        secondary = secondary_count if secondary_count != 'NA' else 0
        meta_vars['total_outcome_measures'] = primary + secondary
    else:
        meta_vars['total_outcome_measures'] = 'NA'

    # 38. primary_to_secondary_ratio
    if primary_count != 'NA' and secondary_count != 'NA' and secondary_count > 0:
        meta_vars['primary_to_secondary_ratio'] = round(primary_count / secondary_count, 2)
    else:
        meta_vars['primary_to_secondary_ratio'] = 'NA'

    # 39. outcome_focus_category - THRESHOLD: Based on primary_to_secondary_ratio
    ratio = meta_vars['primary_to_secondary_ratio']
    if ratio != 'NA':
        if ratio > 1:
            meta_vars['outcome_focus_category'] = 'Primary-focused'
        elif ratio == 1:
            meta_vars['outcome_focus_category'] = 'Balanced'
        else:
            meta_vars['outcome_focus_category'] = 'Secondary-heavy'
    else:
        # Handle cases where ratio couldn't be calculated
        if primary_count != 'NA' and secondary_count != 'NA':
            if primary_count > secondary_count:
                meta_vars['outcome_focus_category'] = 'Primary-focused'
            elif primary_count == secondary_count:
                meta_vars['outcome_focus_category'] = 'Balanced'
            else:
                meta_vars['outcome_focus_category'] = 'Secondary-heavy'
        else:
            meta_vars['outcome_focus_category'] = 'NA'


def generate_safety_meta_variables(meta_vars, raw_vars):
    """Generate safety and adverse events meta-variables (H. SAFETY AND ADVERSE EVENTS META-VARIABLES)"""

    # 40. adverse_events_reporting_completeness
    ae_groups = safe_int_convert(raw_vars.get('adverseEvents_groups_count'), default_na=False)
    serious_ae = safe_int_convert(raw_vars.get('adverseEvents_seriousEvents_count'), default_na=False)
    other_ae = safe_int_convert(raw_vars.get('adverseEvents_otherEvents_count'), default_na=False)

    # Simple completeness score based on availability of AE data
    completeness_score = 0
    if ae_groups != 'NA' and ae_groups > 0:
        completeness_score += 1
    if serious_ae != 'NA':
        completeness_score += 1
    if other_ae != 'NA':
        completeness_score += 1

    meta_vars['adverse_events_reporting_completeness'] = round((completeness_score / 3) * 100, 2)

    # 41. serious_ae_rate (placeholder - would need more detailed AE data)
    meta_vars['serious_ae_rate'] = 'NA'  # Would require participant-level AE data

    # 42. total_ae_rate (placeholder - would need more detailed AE data)
    meta_vars['total_ae_rate'] = 'NA'  # Would require participant-level AE data

    # 43. safety_reporting_category - THRESHOLD: Complete (>80%), Partial (40-80%), Minimal (<40%)
    completeness = meta_vars['adverse_events_reporting_completeness']
    if completeness > 80:
        meta_vars['safety_reporting_category'] = 'Complete'
    elif completeness >= 40:
        meta_vars['safety_reporting_category'] = 'Partial'
    else:
        meta_vars['safety_reporting_category'] = 'Minimal'


def generate_data_quality_meta_variables(meta_vars, raw_vars):
    """Generate data completeness and quality meta-variables (I. DATA COMPLETENESS AND QUALITY META-VARIABLES)"""

    # Define protocol and results modules for completeness scoring
    protocol_modules = [
        'nctId', 'organization_fullName', 'briefTitle', 'overallStatus', 'studyType',
        'phases', 'designInfo_allocation', 'armGroups_count', 'conditions',
        'healthyVolunteers', 'locations_count', 'primaryOutcomes_count',
        'leadSponsor_name', 'oversightHasDmc', 'briefSummary'
    ]

    results_modules = [
        'baseline_total_participants', 'baseline_age_mean', 'baseline_sex_male_count',
        'participantFlow_started_total', 'participantFlow_completed_total',
        'outcomeMeasures_count', 'adverseEvents_groups_count'
    ]

    # 44. protocol_completeness_score
    protocol_available = sum(1 for module in protocol_modules if raw_vars.get(module, 'NA') != 'NA')
    meta_vars['protocol_completeness_score'] = round((protocol_available / len(protocol_modules)) * 100, 2)

    # 45. results_completeness_score
    results_available = sum(1 for module in results_modules if raw_vars.get(module, 'NA') != 'NA')
    meta_vars['results_completeness_score'] = round((results_available / len(results_modules)) * 100, 2)

    # 46. overall_data_completeness_score
    all_modules = protocol_modules + results_modules
    all_available = sum(1 for module in all_modules if raw_vars.get(module, 'NA') != 'NA')
    meta_vars['overall_data_completeness_score'] = round((all_available / len(all_modules)) * 100, 2)

    # 47. data_quality_category - Show percentage ranges
    overall_score = meta_vars['overall_data_completeness_score']
    if overall_score > 90:
        meta_vars['data_quality_category'] = '>90%'
    elif overall_score >= 70:
        meta_vars['data_quality_category'] = '70-90%'
    else:
        meta_vars['data_quality_category'] = '<70%'

    # 48. results_availability_category - Show percentage ranges
    results_score = meta_vars['results_completeness_score']
    if results_score > 80:
        meta_vars['results_availability_category'] = '>80%'
    elif results_score >= 40:
        meta_vars['results_availability_category'] = '40-80%'
    else:
        meta_vars['results_availability_category'] = '<40%'


def generate_subgroup_meta_variables(meta_vars, raw_vars):
    """Generate study characteristics for subgroup analysis (J. STUDY CHARACTERISTICS FOR SUBGROUP ANALYSIS)"""

    # For tertile calculations, we'll use reasonable thresholds based on typical clinical trial data

    # 49. enrollment_size_tertiles - THRESHOLD: Small (<200), Medium (200-600), Large (>600)
    total_participants = safe_int_convert(raw_vars.get('baseline_total_participants'), default_na=False)
    if total_participants != 'NA':
        if total_participants < 200:
            meta_vars['enrollment_size_tertiles'] = 'Small'
        elif total_participants <= 600:
            meta_vars['enrollment_size_tertiles'] = 'Medium'
        else:
            meta_vars['enrollment_size_tertiles'] = 'Large'
    else:
        meta_vars['enrollment_size_tertiles'] = 'NA'

    # 50. study_duration_tertiles - THRESHOLD: Short (<12 months), Medium (12-24 months), Long (>24 months)
    duration = meta_vars.get('study_duration_months', 'NA')
    if duration != 'NA':
        if duration < 12:
            meta_vars['study_duration_tertiles'] = 'Short'
        elif duration <= 24:
            meta_vars['study_duration_tertiles'] = 'Medium'
        else:
            meta_vars['study_duration_tertiles'] = 'Long'
    else:
        meta_vars['study_duration_tertiles'] = 'NA'

    # 51. study_era_category - THRESHOLD: Early (pre-2010), Middle (2010-2015), Recent (>2015)
    start_date = raw_vars.get('startDateStruct_date', 'NA')
    if start_date != 'NA':
        try:
            year = int(str(start_date)[:4])  # Extract year from date
            if year < 2010:
                meta_vars['study_era_category'] = 'Early'
            elif year <= 2015:
                meta_vars['study_era_category'] = 'Middle'
            else:
                meta_vars['study_era_category'] = 'Recent'
        except (ValueError, TypeError):
            meta_vars['study_era_category'] = 'NA'
    else:
        meta_vars['study_era_category'] = 'NA'

    # 52. masking_effectiveness_category
    masking_quality = meta_vars.get('masking_quality_category', 'NA')
    if masking_quality != 'NA':
        if masking_quality in ['Double', 'Triple', 'Quadruple']:
            meta_vars['masking_effectiveness_category'] = 'Effective'
        elif masking_quality == 'Single':
            meta_vars['masking_effectiveness_category'] = 'Limited'
        else:
            meta_vars['masking_effectiveness_category'] = 'None'
    else:
        meta_vars['masking_effectiveness_category'] = 'NA'


def write_meta_variables_csv(meta_variables_data, csv_file_path):
    """Write meta-variables data to CSV file with ordered columns"""

    if not meta_variables_data:
        print("⚠️ No meta-variables data to write")
        return

    # Define column order for meta-variables
    ordered_meta_columns = [
        'study_id',
        # A. Temporal Meta-Variables
        'study_duration_months', 'primary_completion_duration_months',
        'time_to_results_posting_months', 'regulatory_review_time_months',
        # B. Demographic Meta-Variables
        'baseline_age_mean_numeric', 'baseline_age_sd_numeric', 'male_percentage', 'female_percentage',
        'age_category', 'sample_size_category', 'gender_balance_category',
        # C. Study Design Meta-Variables
        'design_complexity_score', 'intervention_complexity_score', 'outcome_complexity_score',
        'masking_quality_category', 'allocation_method', 'study_phase_category',
        'intervention_model_category', 'primary_purpose_category',
        # D. Participant Flow Meta-Variables
        'completion_rate', 'dropout_rate', 'adverse_event_dropout_rate', 'efficacy_dropout_rate', 'lost_followup_rate',
        'completion_rate_category', 'dropout_pattern_category',
        # E. Geographic and Regulatory Meta-Variables
        'number_of_countries', 'number_of_locations', 'locations_per_country',
        'geographic_scope_category', 'regulatory_region_category', 'fda_regulation_category', 'regulatory_oversight_category',
        # F. Sponsorship and Funding Meta-Variables
        'sponsor_type_category', 'collaboration_level_category', 'total_sponsor_entities',
        # G. Outcome and Efficacy Meta-Variables
        'total_outcome_measures', 'primary_to_secondary_ratio', 'outcome_focus_category',
        # H. Safety and Adverse Events Meta-Variables
        'adverse_events_reporting_completeness', 'serious_ae_rate', 'total_ae_rate', 'safety_reporting_category',
        # I. Data Completeness and Quality Meta-Variables
        'protocol_completeness_score', 'results_completeness_score', 'overall_data_completeness_score',
        'data_quality_category', 'results_availability_category',
        # J. Study Characteristics for Subgroup Analysis
        'enrollment_size_tertiles', 'study_duration_tertiles', 'study_era_category', 'masking_effectiveness_category'
    ]

    # Write CSV file
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=ordered_meta_columns)
        writer.writeheader()

        for study_data in meta_variables_data:
            # Fill missing columns with 'NA'
            complete_row = {col: study_data.get(col, 'NA') for col in ordered_meta_columns}
            writer.writerow(complete_row)

    print(f"📊 Meta-variables CSV written with {len(meta_variables_data)} studies and {len(ordered_meta_columns)} variables")
    print(f"📋 Meta-variables ordered by category (A-J)")


def write_categorical_continuous_split(meta_variables_data, output_dir):
    """
    Split meta-variables into categorical and continuous variables for R analysis

    Args:
        meta_variables_data (list): List of meta-variables data
        output_dir (str): Directory to save the split files
    """

    if not meta_variables_data:
        print("⚠️ No meta-variables data to split")
        return

    # Define categorical and continuous variables
    categorical_variables = [
        'study_id',
        # Categorical meta-variables
        'age_category', 'sample_size_category', 'gender_balance_category',
        'masking_quality_category', 'allocation_method', 'study_phase_category',
        'intervention_model_category', 'primary_purpose_category',
        'completion_rate_category', 'dropout_pattern_category',
        'geographic_scope_category', 'regulatory_region_category', 'fda_regulation_category', 'regulatory_oversight_category',
        'sponsor_type_category', 'collaboration_level_category',
        'outcome_focus_category', 'safety_reporting_category',
        'data_quality_category', 'results_availability_category',
        'enrollment_size_tertiles', 'study_duration_tertiles', 'study_era_category', 'masking_effectiveness_category'
    ]

    continuous_variables = [
        'study_id',
        # Continuous meta-variables
        'study_duration_months', 'primary_completion_duration_months',
        'time_to_results_posting_months', 'regulatory_review_time_months',
        'baseline_age_mean_numeric', 'baseline_age_sd_numeric', 'male_percentage', 'female_percentage',
        'design_complexity_score', 'intervention_complexity_score', 'outcome_complexity_score',
        'completion_rate', 'dropout_rate', 'adverse_event_dropout_rate', 'efficacy_dropout_rate', 'lost_followup_rate',
        'number_of_countries', 'number_of_locations', 'locations_per_country',
        'total_sponsor_entities', 'total_outcome_measures', 'primary_to_secondary_ratio',
        'adverse_events_reporting_completeness', 'serious_ae_rate', 'total_ae_rate',
        'protocol_completeness_score', 'results_completeness_score', 'overall_data_completeness_score'
    ]

    # Filter data for categorical variables (exclude summary row)
    categorical_data = []
    for study_data in meta_variables_data:
        if study_data.get('study_id', '').startswith('ALL STUDIES'):
            continue  # Skip summary row
        categorical_row = {var: study_data.get(var, 'NA') for var in categorical_variables}
        categorical_data.append(categorical_row)

    # Filter data for continuous variables (exclude summary row)
    continuous_data = []
    for study_data in meta_variables_data:
        if study_data.get('study_id', '').startswith('ALL STUDIES'):
            continue  # Skip summary row
        continuous_row = {var: study_data.get(var, 'NA') for var in continuous_variables}
        continuous_data.append(continuous_row)

    # Write categorical variables CSV (transposed)
    categorical_csv_path = os.path.join(output_dir, 'all_categorical_variables.csv')
    write_transposed_csv(categorical_data, categorical_csv_path)

    # Write continuous variables CSV (transposed)
    continuous_csv_path = os.path.join(output_dir, 'all_continuous_variables.csv')
    write_transposed_csv(continuous_data, continuous_csv_path)

    print(f"📊 Categorical variables CSV: {len(categorical_data)} studies, {len(categorical_variables)} variables")
    print(f"📊 Continuous variables CSV: {len(continuous_data)} studies, {len(continuous_variables)} variables")


def generate_characteristics_table1(raw_variables_data, meta_variables_data):
    """
    Generate Table 1: Characteristics of Included Studies with summary statistics

    Args:
        raw_variables_data (list): List of raw variables data
        meta_variables_data (list): List of meta-variables data

    Returns:
        list: List of dictionaries with characteristics data including summary row
    """

    if not raw_variables_data or not meta_variables_data:
        return []

    # Define the 20 most critical characteristics
    characteristics_columns = [
        'study_id',
        'study_phase_category',
        'allocation_method',
        'masking_quality_category',
        'study_duration_months',
        'baseline_total_participants',
        'baseline_age_mean_numeric',
        'male_percentage',
        'conditions',
        'armGroups_count',
        'interventions_names',
        'primaryOutcomes_count',
        'completion_rate',
        'dropout_rate',
        'number_of_countries',
        'geographic_scope_category',
        'sponsor_type_category',
        'leadSponsor_name',
        'data_quality_category',
        'results_availability_category'
    ]

    # Combine raw and meta variables for each study
    characteristics_data = []

    for i, raw_vars in enumerate(raw_variables_data):
        meta_vars = meta_variables_data[i] if i < len(meta_variables_data) else {}

        # Create combined characteristics row
        char_row = {}
        for col in characteristics_columns:
            # Try to get from meta-variables first, then raw variables
            if col in meta_vars:
                char_row[col] = meta_vars[col]
            elif col in raw_vars:
                char_row[col] = raw_vars[col]
            else:
                char_row[col] = 'NA'

        characteristics_data.append(char_row)

    # Generate summary statistics as first row
    summary_row = generate_summary_statistics(characteristics_data, characteristics_columns)

    # Insert summary row at the beginning
    final_data = [summary_row] + characteristics_data

    return final_data


def generate_summary_statistics(characteristics_data, columns):
    """Generate summary statistics for the first row of characteristics table"""

    summary_row = {}
    n_studies = len(characteristics_data)

    for col in columns:
        if col == 'study_id':
            summary_row[col] = f"ALL STUDIES (n={n_studies})"
            continue

        # Get all values for this column (excluding NA)
        values = [row.get(col, 'NA') for row in characteristics_data]
        non_na_values = [v for v in values if v != 'NA' and v != '' and v is not None]

        if not non_na_values:
            summary_row[col] = 'NA'
            continue

        # Handle different variable types
        if col in ['study_duration_months', 'baseline_total_participants', 'baseline_age_mean_numeric',
                   'male_percentage', 'completion_rate', 'dropout_rate', 'number_of_countries', 'primaryOutcomes_count', 'armGroups_count']:
            # Continuous variables - calculate mean ± SD
            try:
                numeric_values = [float(v) for v in non_na_values if str(v).replace('.', '').replace('-', '').isdigit()]
                if numeric_values:
                    mean_val = sum(numeric_values) / len(numeric_values)
                    if len(numeric_values) > 1:
                        variance = sum((x - mean_val) ** 2 for x in numeric_values) / (len(numeric_values) - 1)
                        sd_val = variance ** 0.5
                        summary_row[col] = f"Mean: {mean_val:.1f} +/- {sd_val:.1f}"
                    else:
                        summary_row[col] = f"Mean: {mean_val:.1f}"
                else:
                    summary_row[col] = 'NA'
            except (ValueError, TypeError):
                summary_row[col] = 'NA'

        elif col in ['study_phase_category', 'allocation_method', 'masking_quality_category',
                     'geographic_scope_category', 'sponsor_type_category', 'data_quality_category', 'results_availability_category']:
            # Categorical variables - show most common category with count
            from collections import Counter
            counts = Counter(non_na_values)
            if counts:
                most_common = counts.most_common(1)[0]
                summary_row[col] = f"{most_common[0]} ({most_common[1]}/{n_studies})"
            else:
                summary_row[col] = 'NA'

        elif col in ['conditions', 'interventions_names', 'leadSponsor_name']:
            # Text variables - show "Multiple" or most common
            unique_values = list(set(non_na_values))
            if len(unique_values) == 1:
                # All studies have same value
                summary_row[col] = unique_values[0][:50] + "..." if len(unique_values[0]) > 50 else unique_values[0]
            else:
                summary_row[col] = f"Multiple ({len(unique_values)} different)"

        else:
            # Default handling
            summary_row[col] = f"Available for {len(non_na_values)}/{n_studies} studies"

    return summary_row


def write_characteristics_csv(characteristics_data, csv_file_path):
    """Write characteristics data to CSV file"""

    if not characteristics_data:
        print("⚠️ No characteristics data to write")
        return

    # Get column names from first row
    columns = list(characteristics_data[0].keys())

    # Write CSV file
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=columns)
        writer.writeheader()

        for row_data in characteristics_data:
            writer.writerow(row_data)

    print(f"📊 Characteristics CSV written with {len(characteristics_data)-1} studies + 1 summary row and {len(columns)} variables")
    print(f"📋 Table 1: Characteristics of Included Studies generated")


def write_transposed_csv(data, csv_file_path, include_summary=False):
    """
    Write data to CSV file in transposed format (variables as rows, studies as columns)

    Args:
        data (list): List of dictionaries containing data
        csv_file_path (str): Path where CSV file should be saved
        include_summary (bool): Whether first row is summary statistics
    """

    if not data:
        print("⚠️ No data to write")
        return

    # Get all column names from first row
    columns = list(data[0].keys())

    # Prepare transposed data
    transposed_rows = []

    for col in columns:
        row = [col]  # Start with variable name
        for study_data in data:
            row.append(study_data.get(col, 'NA'))
        transposed_rows.append(row)

    # Create header row (study IDs)
    if include_summary:
        header = ['Variable'] + [row.get('study_id', f'Study_{i}') for i, row in enumerate(data)]
    else:
        header = ['Variable'] + [row.get('study_id', f'Study_{i}') for i, row in enumerate(data)]

    # Write CSV file
    with open(csv_file_path, 'w', newline='', encoding='utf-8') as csvfile:
        writer = csv.writer(csvfile)

        # Don't write the header row as requested
        # writer.writerow(header)

        # Write transposed data
        for row in transposed_rows:
            writer.writerow(row)

    print(f"📊 Transposed CSV written: {csv_file_path}")


def consolidate_adverse_events_csv(studies_dir):
    """
    Consolidate all adverse_events.csv files from NCT folders into one master CSV

    Args:
        studies_dir (str): Path to directory containing NCT study folders

    Returns:
        str: Path to consolidated adverse events CSV file
    """

    try:
        print("📊 Consolidating adverse events data...")

        # Get all NCT study directories
        study_dirs = [d for d in os.listdir(studies_dir)
                     if os.path.isdir(os.path.join(studies_dir, d)) and d.startswith('NCT')]

        if not study_dirs:
            print("⚠️ No NCT study directories found")
            return None

        consolidated_data = []

        for study_id in study_dirs:
            ae_csv_path = os.path.join(studies_dir, study_id, 'adverse_events.csv')

            if os.path.exists(ae_csv_path):
                try:
                    with open(ae_csv_path, 'r', encoding='utf-8') as csvfile:
                        reader = csv.DictReader(csvfile)
                        for row in reader:
                            # Add study ID to each adverse event
                            row['study_id'] = study_id
                            consolidated_data.append(row)
                except Exception as e:
                    print(f"⚠️ Error reading {ae_csv_path}: {e}")
                    continue

        if not consolidated_data:
            print("⚠️ No adverse events data found")
            return None

        # Write consolidated CSV
        consolidated_csv_path = os.path.join(studies_dir, 'all_adverse_events.csv')

        if consolidated_data:
            fieldnames = consolidated_data[0].keys()
            with open(consolidated_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(consolidated_data)

            print(f"📊 Consolidated adverse events CSV: {len(consolidated_data)} events from {len(study_dirs)} studies")
            return consolidated_csv_path

        return None

    except Exception as e:
        print(f"❌ Error consolidating adverse events: {e}")
        return None


def generate_drug_analysis_files(studies_dir):
    """
    Generate analysis files for a specific drug folder during extraction

    Args:
        studies_dir (str): Path to directory containing NCT study folders (e.g., output/diabetes/canagliflozin)

    Returns:
        dict: Success status and file paths
    """

    try:
        print(f"📊 Generating analysis files for drug folder: {studies_dir}")

        # Get all NCT study directories
        study_dirs = [d for d in os.listdir(studies_dir)
                     if os.path.isdir(os.path.join(studies_dir, d)) and d.startswith('NCT')]

        if not study_dirs:
            return {'success': False, 'error': 'No NCT study directories found'}

        print(f"📁 Found {len(study_dirs)} studies to analyze")

        # Phase 1: Extract raw variables
        print("🔧 Extracting raw variables...")
        raw_variables_data = []
        for i, study_id in enumerate(study_dirs, 1):
            study_path = os.path.join(studies_dir, study_id)
            raw_variables = extract_raw_variables_from_json(study_path, study_id)
            raw_variables_data.append(raw_variables)

        # Phase 2: Generate meta-variables
        print("🔧 Generating meta-variables...")
        meta_variables_data = generate_meta_variables_from_raw(raw_variables_data)

        # Phase 3: Generate characteristics table
        print("🔧 Generating characteristics table...")
        characteristics_data = generate_characteristics_table1(raw_variables_data, meta_variables_data)

        # Write transposed CSV files directly in drug folder
        raw_csv_path = os.path.join(studies_dir, 'raw_variables.csv')
        meta_csv_path = os.path.join(studies_dir, 'meta_variables.csv')
        characteristics_csv_path = os.path.join(studies_dir, 'characteristics_of_included_studies.csv')

        write_transposed_csv(raw_variables_data, raw_csv_path)
        write_transposed_csv(meta_variables_data, meta_csv_path)
        write_transposed_csv(characteristics_data, characteristics_csv_path, include_summary=True)

        # Consolidate adverse events data
        print("🔧 Consolidating adverse events data...")
        ae_csv_path = consolidate_adverse_events_csv(studies_dir)

        print(f"✅ Analysis files generated for {len(study_dirs)} studies")

        return {
            'success': True,
            'raw_csv_file': raw_csv_path,
            'meta_csv_file': meta_csv_path,
            'characteristics_csv_file': characteristics_csv_path,
            'adverse_events_csv_file': ae_csv_path,
            'studies_processed': len(study_dirs),
            'message': f'Drug analysis files generated for {len(study_dirs)} studies'
        }

    except Exception as e:
        print(f"❌ Error generating drug analysis files: {e}")
        return {'success': False, 'error': str(e)}


def generate_complete_table1(studies_dir):
    """
    Generate both raw variables and meta-variables Table 1 CSVs from studies directory

    Args:
        studies_dir (str): Path to directory containing NCT study folders

    Returns:
        dict: Success status and file path information
    """

    try:
        print("📊 Generating Complete Table 1: Raw Variables + Meta-Variables")
        print("🔧 Phase 1: Extracting raw variables from JSON modules")

        # Get all NCT study directories
        study_dirs = [d for d in os.listdir(studies_dir)
                     if os.path.isdir(os.path.join(studies_dir, d)) and d.startswith('NCT')]

        if not study_dirs:
            return {'success': False, 'error': 'No NCT study directories found'}

        print(f"📁 Found {len(study_dirs)} studies to process")

        # Phase 1: Extract raw variables from each study
        raw_variables_data = []
        for i, study_id in enumerate(study_dirs, 1):
            print(f"🔍 Processing {study_id} ({i}/{len(study_dirs)}) - Raw variables")

            study_path = os.path.join(studies_dir, study_id)
            raw_variables = extract_raw_variables_from_json(study_path, study_id)
            raw_variables_data.append(raw_variables)

        # Phase 2: Generate meta-variables from raw variables
        print("🔧 Phase 2: Generating meta-variables from raw variables")
        meta_variables_data = generate_meta_variables_from_raw(raw_variables_data)

        # Phase 3: Generate characteristics table with summary statistics
        print("🔧 Phase 3: Generating Table 1 characteristics with summary statistics")
        characteristics_data = generate_characteristics_table1(raw_variables_data, meta_variables_data)

        # Determine output directory
        output_dir = os.path.dirname(studies_dir)
        if 'output_meta' in output_dir:
            # Extract project name from path like output_meta/diabetes/canagliflozin
            path_parts = output_dir.split(os.sep)
            if len(path_parts) >= 2:
                project_name = path_parts[-2] if path_parts[-2] != 'output_meta' else path_parts[-1]
                csv_output_dir = os.path.join('output_meta', project_name, 'R_output')
            else:
                csv_output_dir = os.path.join(output_dir, 'R_output')
        else:
            csv_output_dir = os.path.join(output_dir, 'meta_analysis_output')

        os.makedirs(csv_output_dir, exist_ok=True)

        # Generate all three CSV files
        raw_csv_path = os.path.join(csv_output_dir, 'table1_raw_variables.csv')
        meta_csv_path = os.path.join(csv_output_dir, 'table1_meta_variables.csv')
        characteristics_csv_path = os.path.join(csv_output_dir, 'table1_characteristics_of_included_studies.csv')

        # Write transposed CSV files for R_output (table1_ prefix)
        write_transposed_csv(raw_variables_data, raw_csv_path)
        write_transposed_csv(meta_variables_data, meta_csv_path)
        write_transposed_csv(characteristics_data, characteristics_csv_path, include_summary=True)

        # Generate categorical and continuous variable splits for R analysis
        write_categorical_continuous_split(meta_variables_data, csv_output_dir)

        return {
            'success': True,
            'raw_csv_file': raw_csv_path,
            'meta_csv_file': meta_csv_path,
            'characteristics_csv_file': characteristics_csv_path,
            'studies_processed': len(study_dirs),
            'message': f'Complete Table 1 generated with {len(study_dirs)} studies'
        }

    except Exception as e:
        print(f"❌ Error generating complete Table 1: {e}")
        return {'success': False, 'error': str(e)}


if __name__ == "__main__":
    # Test both raw variables and meta-variables extraction
    import sys
    if len(sys.argv) > 1:
        studies_directory = sys.argv[1]
        result = generate_complete_table1(studies_directory)
        if result['success']:
            print(f"✅ {result['message']}")
            print(f"📁 Raw variables CSV: {result['raw_csv_file']}")
            print(f"📁 Meta-variables CSV: {result['meta_csv_file']}")
            print(f"📁 Characteristics CSV: {result['characteristics_csv_file']}")
        else:
            print(f"❌ Error: {result['error']}")
    else:
        print("Usage: python meta_analysis_table1.py <studies_directory>")
